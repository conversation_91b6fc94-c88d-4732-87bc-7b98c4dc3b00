import { Info } from '../FieldTypes'
import { TenantEntity, TenantEntityInfo } from './TenantEntity'
import { AssignableRole } from './AssignableRole'
import { OrganizationHierarchy } from './OrganizationHierarchy'

export interface RoleAssignment extends TenantEntity {
  assignableRoleId: string
  employeeId: string
  isInheritable: boolean
  organizationId?: string
  assignableRole?: AssignableRole
  organizationHierarchy?: OrganizationHierarchy
  employee?: any
}

export const roleAssignmentInfo: Info<RoleAssignment> = {
  typeName: 'Role Assignment',
  nameKey: 'employeeId',
  sortKey: 'assignableRoleId',
  sortAsc: true,
  backend: 'BaseBiz',
  endpoint: 'RoleAssignment',
  fields: {
    assignableRoleId: { label: 'Assignable Role', type: 'external', required: true },
    employeeId: { label: 'Employee', type: 'external', required: true },
    isInheritable: { label: 'Inheritable', type: 'bool', required: true },
    organizationId: { label: 'Organization', type: 'external', disabled: true },
    assignableRole: { label: 'Assignable Role Object', type: 'objlist' },
    organizationHierarchy: { label: 'Organization Hierarchy Object', type: 'objlist' },
    employee: { label: 'Employee Object', type: 'objlist' },
    ...TenantEntityInfo.fields,
  },
  options: {
    assignableRoleId: {
      entity: 'AssignableRole',
      joins: {
        organizationId: 'organizationId',
      },
    },
    employeeId: {
      entity: 'Employee',
      valueKey: 'id',
    },
    organizationId: { entity: 'OrganizationHierarchy' },
    ...TenantEntityInfo.options,
  },
  default: {
    assignableRoleId: '',
    employeeId: '',
    isInheritable: true,
    organizationId: '',
  },
  columnsShown: new Set(['assignableRoleId', 'employeeId', 'isInheritable']),
  formLayout: [['assignableRoleId', 'employeeId'], ['isInheritable']],
}
