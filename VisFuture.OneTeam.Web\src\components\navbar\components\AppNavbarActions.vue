<script lang="ts" setup>
import { useUserStore } from '../../../stores/user-store'
import { storeToRefs } from 'pinia'

import ProfileDropdown from './dropdowns/ProfileDropdown.vue'
import NotificationDropdown from './dropdowns/NotificationDropdown.vue'

defineProps({
  isMobile: { type: Boolean, default: false },
})

const userStore = useUserStore()
const { userName } = storeToRefs(userStore)
</script>

<template>
  <div class="app-navbar-actions">
    <span class="app-navbar-actions__item username-display">
      {{ userName }}
    </span>
    <NotificationDropdown class="app-navbar-actions__item" />

    <ProfileDropdown class="app-navbar-actions__item app-navbar-actions__item--profile mr-1" />
  </div>
</template>

<style lang="scss">
.app-navbar-actions {
  display: flex;
  align-items: center;

  .va-dropdown__anchor {
    color: var(--va-primary);
    fill: var(--va-primary);
  }

  &__item {
    padding: 0;
    margin-left: 0.25rem;
    margin-right: 0.25rem;

    svg {
      height: 20px;
    }

    &--profile {
      display: flex;
      justify-content: center;
    }

    .va-dropdown-content {
      background-color: var(--va-white);
    }

    @media screen and (max-width: 640px) {
      margin-left: 0;
      margin-right: 0;

      &:first-of-type {
        margin-left: 0;
      }
    }
  }

  .username-display {
    font-weight: 600;
    color: var(--va-primary);
  }
}
</style>
