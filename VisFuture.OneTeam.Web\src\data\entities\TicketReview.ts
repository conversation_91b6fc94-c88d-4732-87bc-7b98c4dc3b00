import { Info } from '../FieldTypes'
import { TenantEntity, TenantEntityInfo } from './TenantEntity'

export interface TicketReview extends TenantEntity {
  ticketId: string
  reason: string
  addedBy?: string
  reviewedOn?: Date
  reviewedBy?: string
}

export const ticketReviewInfo: Info<TicketReview> = {
  typeName: 'Ticket Review',
  nameKey: 'reason',
  sortKey: 'reviewedOn',
  backend: 'Ticket',
  endpoint: 'TicketReview',
  fields: {
    ticketId: { label: 'Ticket', type: 'external', required: true },
    reason: { label: 'Reason', type: 'bigtext', required: true },
    addedBy: { label: 'Added By', type: 'smalltext' },
    reviewedOn: { label: 'Reviewed On', type: 'date' },
    reviewedBy: { label: 'Reviewed By', type: 'smalltext' },
    ...TenantEntityInfo.fields,
  },
  options: {
    ticketId: { entity: 'Ticket' },
    ...TenantEntityInfo.options,
  },
  default: {
    ticketId: '',
    reason: '',
  },
  columnsShown: new Set(['reason', 'addedBy', 'reviewedOn', 'reviewedBy']),
  formLayout: [
    ['ticketId', 'reason'],
    ['addedBy', 'reviewedOn', 'reviewedBy'],
  ],
}
