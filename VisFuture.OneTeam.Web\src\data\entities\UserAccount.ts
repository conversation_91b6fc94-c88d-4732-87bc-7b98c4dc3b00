import { Info } from '../FieldTypes'
import { BaseEntity, BaseEntityInfo } from './BaseEntity'

export interface UserAccount extends BaseEntity {
  name: string
  passwordHash: string
  email: string
  mobile?: string
  mfa: boolean
  isActive: boolean
  description?: string
  imageId?: string
  language?: string
}

export const userAccountInfo: Info<UserAccount> = {
  typeName: 'User Account',
  nameKey: 'name',
  sortKey: 'name',
  sortAsc: true,
  backend: 'BaseBiz',
  endpoint: 'UserAccount',
  fields: {
    name: { label: 'Login Name', type: 'smalltext', required: true },
    passwordHash: { label: 'Password Hash', type: 'smalltext', required: true },
    email: { label: 'Email', type: 'email', required: true },
    mobile: { label: 'Mobile', type: 'smalltext' },
    mfa: { label: 'MFA Enabled', type: 'bool', required: true },
    isActive: { label: 'Active', type: 'bool', required: true },
    description: { label: 'Description', type: 'textarea' },
    imageId: { label: 'Profile Image', type: 'bigtext' },
    language: { label: 'Language', type: 'smalltext' },
    ...BaseEntityInfo.fields,
  },
  options: {
    ...BaseEntityInfo.options,
  },
  default: {
    name: '',
    passwordHash: '',
    email: '',
    mfa: false,
    isActive: true,
  },
  columnsShown: new Set(['name', 'email', 'mfa', 'isActive']),
  formLayout: [['name', 'email'], ['mfa', 'isActive'], ['description']],
}
