import { BaseEntity } from '../data/entities/BaseEntity'
import { schema, SchemaKey } from '../data/schema'
import { EntityPage } from './entityPage'

export const sleep = (ms = 0) => {
  return new Promise((resolve) => setTimeout(resolve, ms))
}

/** Validation */
export const validators = {
  email: (v: string) => {
    const pattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return !v || pattern.test(v) || 'Please enter a valid email address'
  },
  tel: (v: string) => {
    const pattern = /^[\d()+\-#*\s]*$/
    return !v || pattern.test(v) || 'Please enter a valid telephone/fax number'
  },
  maxLength: (max: number) => (v: string) => !v || v.length <= max || `This field must be at most ${max} characters`,
  percent: (v: string) => !v || (parseFloat(v) >= 0 && parseFloat(v) < 100) || 'Please enter a valid percentage',
  required: (v: any) => !!v || 'This field is required',
}

export const capitalize = (s: string) => {
  return s.charAt(0).toUpperCase() + s.slice(1)
}

export const uncapitalize = (s: string) => {
  return s.charAt(0).toLowerCase() + s.slice(1)
}

export const titleCase = (str: string) => {
  return str
    .toLowerCase()
    .split(' ')
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ')
}

export const round = (n: number) => Math.round((n + Number.EPSILON) * 100) / 100
export const trunc = (str: string | undefined | null, n: number) => {
  if (!str) {
    return '<none>'
  }
  if (str.length < n) {
    return str
  }
  return str.substring(0, n - 1) + '…'
}
export const getTypeName = (type: SchemaKey) => schema[type].typeShortName ?? schema[type].typeName

export const saveOrAdd = (page: EntityPage<BaseEntity>) => (page.entityToEdit.value ? 'Save' : 'Add')
export const editOrAdd = (page: EntityPage<BaseEntity>) => (page.entityToEdit.value ? 'Edit' : 'Add')
