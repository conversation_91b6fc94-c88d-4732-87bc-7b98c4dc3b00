import { Info } from '../FieldTypes'
import { TenantEntity, TenantEntityInfo } from './TenantEntity'

export interface TicketBillingDelivery extends TenantEntity {
  ticketBillingId: string
  description: string
  sequence?: string
  estimatedDate?: Date
}

export const ticketBillingDeliveryInfo: Info<TicketBillingDelivery> = {
  typeName: 'Ticket Billing Delivery',
  nameKey: 'description',
  sortKey: 'estimatedDate',
  backend: 'Ticket',
  endpoint: 'BillingDelivery',
  fields: {
    ticketBillingId: { label: 'Ticket Billing', type: 'external', required: true },
    description: { label: 'Description', type: 'bigtext', required: true },
    sequence: { label: 'Sequence', type: 'smalltext' },
    estimatedDate: { label: 'Estimated Date', type: 'date' },
    ...TenantEntityInfo.fields,
  },
  options: {
    ticketBillingId: { entity: 'TicketBilling' },
    ...TenantEntityInfo.options,
  },
  default: {
    ticketBillingId: '',
    description: '',
  },
  columnsShown: new Set(['description', 'sequence', 'estimatedDate']),
  formLayout: [['description', 'sequence'], ['estimatedDate']],
}
