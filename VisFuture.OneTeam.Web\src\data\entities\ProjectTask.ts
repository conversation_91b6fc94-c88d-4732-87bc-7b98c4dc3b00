import { Info } from '../FieldTypes'
import { TenantEntity, TenantEntityInfo } from './TenantEntity'

export interface ProjectTask extends TenantEntity {
  projectId: string
  name: string
  wbs: string
  startDate: Date
  endDate?: Date
  assigneeId: string
  status: string
  milestone: boolean
  description?: string
}

export const Statuses = ['New', 'In Progress', 'Completed'] as const
export type Status = (typeof Statuses)[number]

export const projectTaskInfo: Info<ProjectTask> = {
  typeName: 'Project Task',
  nameKey: 'name',
  sortKey: 'wbs',
  sortAsc: true,
  backend: 'Project',
  endpoint: 'Task',
  fields: {
    projectId: { label: 'Project', type: 'external', required: true },
    wbs: { label: 'WBS', type: 'bigtext', required: true },
    name: { label: 'Task Name', type: 'bigtext', required: true },
    startDate: { label: 'Start Date', type: 'date' },
    endDate: { label: 'End Date', type: 'date' },
    status: { label: 'Status', type: 'codetype', required: true },
    assigneeId: { label: 'Assign To', type: 'external', required: true },
    milestone: { label: 'Milestone', type: 'bool', required: true },
    description: { label: 'Description', type: 'textarea' },
    ...TenantEntityInfo.fields,
  },
  options: {
    projectId: { entity: 'Project' },
    assigneeId: { entity: 'Employee' },
    status: { typeCode: 'ProjectTaskStatus' },

    ...TenantEntityInfo.options,
  },
  default: {
    projectId: '',
    name: '',
    wbs: '',
    startDate: new Date(),
    assigneeId: '',
    status: '01',
    milestone: false,
  },
  columnsShown: new Set(['projectId', 'wbs', 'name', 'status', 'assigneeId', 'startDate', 'endDate', 'milestone']),
  formLayout: [
    ['projectId', 'wbs'],
    ['name', 'assigneeId'],
    ['startDate', 'endDate'],
    ['status', 'milestone'],
    ['description'],
  ],
}
