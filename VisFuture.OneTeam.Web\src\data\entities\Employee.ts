import { Info } from '../FieldTypes'
import { CodeEntity } from './CodeEntity'
import { OrganizationEmployee } from './OrganizationEmployee'
import { RoleAssignment } from './RoleAssignment'
import { TenantEntityInfo } from './TenantEntity'
import { UserAccount } from './UserAccount'
import { OrganizationHierarchy } from './OrganizationHierarchy'

export const MainTypes = ['FullTime', 'Intern', 'Contractor'] as const
export type MainType = (typeof MainTypes)[number]
export const EmployeeStatuses = ['Hired', 'Terminated', 'Pre-hired'] as const
export type EmployeeStatus = (typeof EmployeeStatuses)[number]

export interface Employee extends CodeEntity {
  name: string
  mainType: string
  subType?: string
  description?: string
  positionId?: string
  jobTitle?: string
  businessEmail: string
  workScheduleId?: string
  hireDate: Date
  terminateDate?: Date
  employeeStatus: string
  userAccountId?: string
  isTenantAdmin: boolean
  isActive: boolean
  organizationEmployees?: OrganizationEmployee[]
  roleAssignments?: RoleAssignment[]
  userAccounts?: UserAccount[]
  organizationId?: string
  organization?: OrganizationHierarchy[]
}

export const employeeInfo: Info<Employee> = {
  typeName: 'Employee',
  nameKey: 'name',
  sortKey: 'code',
  sortAsc: true,
  backend: 'BaseBiz',
  endpoint: 'Employee',
  fields: {
    code: { label: 'Code', type: 'smalltext', disabled: true },
    name: { label: 'Name', type: 'bigtext', required: true },
    mainType: { label: 'Main Type', type: 'codetype', required: true },
    subType: { label: 'Sub Type', type: 'smalltext' },
    description: { label: 'Description', type: 'textarea' },
    positionId: { label: 'Position', type: 'bigtext' },
    jobTitle: { label: 'Job Position', type: 'codetype' },
    businessEmail: { label: 'Business Email', type: 'email', required: true },
    workScheduleId: { label: 'Work Schedule', type: 'bigtext' },
    employeeStatus: { label: 'Status', type: 'codetype', required: true },
    hireDate: { label: 'Hire Date', type: 'date', required: true },
    terminateDate: { label: 'Termination Date', type: 'date' },
    userAccountId: { label: 'User Account', type: 'external' },
    isTenantAdmin: { label: 'Tenant Admin', type: 'bool', required: true },
    isActive: { label: 'Active', type: 'bool', required: true },
    organizationEmployees: { label: 'Organizations', type: 'objlist' },
    roleAssignments: { label: 'Role Assignments', type: 'objlist' },
    userAccounts: { label: 'User Accounts', type: 'objlist' },
    organizationId: { label: 'Organization', type: 'external', required: true },
    organization: { label: 'Resolved Organization', type: 'external' },
    ...TenantEntityInfo.fields,
  },
  options: {
    userAccountId: {
      entity: 'UserAccount',
      sync: (x, extern?) => {
        if (!extern?.value) return
        const userAccount = extern.value as UserAccount
        x.value.businessEmail = userAccount.email
        x.value.isActive = userAccount.isActive
      },
    },
    mainType: { typeCode: 'EmployeeMainType' },
    employeeStatus: { typeCode: 'EmployeeStatus' },
    jobTitle: { typeCode: 'JobPosition' },
    organizationId: {
      entity: 'OrganizationHierarchy',
    },
    organization: {
      entity: 'OrganizationHierarchy',
      filteredEndpoint: 'OrganizationEmployeeList',
      extraInfo: {
        employeeId: 'id', // maps current Employee.id into the request fieldValues
      },
    },
    ...TenantEntityInfo.options,
  },
  default: {
    code: 'Auto-generated',
    name: '',
    mainType: '',
    hireDate: new Date(),
    businessEmail: '',
    employeeStatus: '',
    isTenantAdmin: false,
    isActive: true,
  },
  columnsShown: new Set([
    'code',
    'name',
    'mainType',
    'jobTitle',
    'employeeStatus',
    'hireDate',
    'businessEmail',
    'isActive',
  ]),
  tabList: {
    OrganizationHierarchy: {
      tabName: 'Organization',
      relation: { id: 'organizationId' },
    },
    UserAccount: {
      tabName: 'User Accounts',
      relation: { id: 'userAccountId' },
    },
  },
  formLayout: [
    ['code', 'name'],
    ['mainType', 'employeeStatus'],
    ['jobTitle', 'businessEmail'],
    ['hireDate', 'terminateDate'],
    ['description'],
    ['userAccountId', 'isActive', 'isTenantAdmin'],
    ['organizationId'],
  ],
}
