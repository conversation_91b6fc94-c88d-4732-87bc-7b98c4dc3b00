import { Info } from '../FieldTypes'
import { TenantEntity, TenantEntityInfo } from './TenantEntity'

export interface NotificationTemplate extends TenantEntity {
  notificationType: string
  notificationMethod: string
  title?: string
  body: string
  sender?: string
}

export const notificationTemplateInfo: Info<NotificationTemplate> = {
  typeName: 'Notification Template',
  nameKey: 'notificationType',
  sortKey: 'notificationType',
  sortAsc: true,
  backend: 'BaseBiz',
  endpoint: 'NotificationTemplate',
  fields: {
    notificationType: { label: 'Notification Type', type: 'codetype', required: true },
    notificationMethod: { label: 'Notification Method', type: 'codetype', required: true },
    title: { label: 'Title', type: 'smalltext' },
    sender: { label: 'Sender', type: 'smalltext' },
    body: { label: 'Body', type: 'textarea', required: true },
    ...TenantEntityInfo.fields,
  },
  options: {
    notificationType: { typeCode: 'NotificationType' },
    notificationMethod: { typeCode: 'NotificationMethod' },
    ...TenantEntityInfo.options,
  },
  default: {
    notificationType: '',
    notificationMethod: '',
    title: '',
    sender: '',
    body: '',
  },
  columnsShown: new Set(['notificationType', 'notificationMethod', 'title', 'sender', 'body']),
  formLayout: [['notificationType', 'notificationMethod'], ['title', 'sender'], ['body']],
}
