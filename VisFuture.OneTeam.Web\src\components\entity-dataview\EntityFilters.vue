<script setup lang="ts">
import { Ref, ref, watch } from 'vue'
import { authedRequest } from '../../services/api/requests'
import { BaseEntity } from '../../data/entities/BaseEntity'
import { schema, Schema<PERSON>ey } from '../../data/schema'
import { BuildApi } from '../../services/api/useApi'
import { EntityPage } from '../../services/entityPage'
import { trunc } from '../../services/utils'
import { getExternMap, isExtern, getEntityName } from '../../services/externUtils'
import { CHIP_SIZE } from '../../data/Constants'

import FieldValueFilter from './FieldValueFilter.vue'

const props = defineProps<{
  type: SchemaKey
  api: BuildApi<BaseEntity>
  page: EntityPage<BaseEntity>
}>()

const sharedFilters = props.api.filters
const info = schema[props.type]
const leftStacking = info.defaultFilter ? 'md' : 'sm'

const filters = ref(sharedFilters.value.fieldValues!)
const defaultFilter = info.defaultFilter ?? null
const showFilters = ref(false)
const filterBy = ref(defaultFilter) as Ref<string | null> // column to filter by
const fieldValue = ref(null) as Ref<string | null> // value in the column to filter by
const filterMirror = ref({}) as Ref<{ [key: string]: boolean }> // data structure that mirrors the filter (chip closing logic)

const valueList = ref([]) as Ref<(string | null)[]>
const listHasNull = ref(false)
const externList = ref<Record<string, BaseEntity>>({})

// get unique values for the selected field
watch(
  filterBy,
  async () => {
    fieldValue.value = null
    if (!filterBy.value) {
      return
    }
    valueList.value = (
      await authedRequest(info.backend, '/' + info.endpoint + 'List', 'POST', {
        uniqueFromField: filterBy.value,
      })
    ).uniqueValues!
    listHasNull.value = valueList.value.includes(null)
    if (isExtern(props.type, filterBy.value!)) {
      externList.value = await getExternMap(
        valueList.value.filter((x) => x != null) as string[],
        props.type,
        filterBy.value!,
      )
    }
  },
  { immediate: true },
)

// show filter pane when filter button pressed
watch(showFilters, () => {
  if (!showFilters.value) {
    filterBy.value = defaultFilter
    fieldValue.value = null
    for (const key in filters.value) {
      delete filters.value[key]
    }
  } else {
    filterBy.value = null
    fieldValue.value = null
  }
})

// remove filters when chip is deleted
watch(filterMirror.value, () => {
  for (const key in filterMirror.value) {
    if (!filterMirror.value[key]) {
      delete filterMirror.value[key]
      delete filters.value[key]
    }
  }
})
// watch for changes if a default filter is set
watch(fieldValue, () => {
  if (defaultFilter && !showFilters.value) {
    if (fieldValue.value) {
      filters.value[filterBy.value!] = new Set([fieldValue.value])
      filterMirror.value[filterBy.value!] = true
    } else {
      delete filters.value[filterBy.value!]
      delete filterMirror.value[filterBy.value!]
    }
  }
})

// get the chip text for the filter
const getChip = (key: string, values: Set<string | null>) => {
  const keyName = info.fields[key].label
  const textSize = CHIP_SIZE / (values.size + 1)
  let valName: string
  if (isExtern(props.type, key)) {
    valName = Array.from(values)
      .map((val) => trunc(val ? getEntityName(externList.value[val], props.type) : null, textSize))
      .join(', ')
  } else {
    valName = Array.from(values)
      .map((val) => trunc(val, textSize))
      .join(', ')
  }
  return `${trunc(keyName, textSize)}: ${valName}`
}

// make the fields for the filter dropdown
const makeFields = () => {
  return Object.keys(info.fields)
    .filter((key) => info.fields[key].type !== 'objlist')
    .map((key) => ({
      text: info.fields[key].label,
      value: key,
    }))
}

// add the filter to the filters object
const onFilterAdd = () => {
  if (filters.value[filterBy.value!]) {
    filters.value[filterBy.value!].add(fieldValue.value!)
  } else {
    filters.value[filterBy.value!] = new Set([fieldValue.value!])
  }
  filterMirror.value[filterBy.value!] = true
  filterBy.value = null
  fieldValue.value = null
}
</script>

<template>
  <div class="flex flex-col sm:flex-row gap-2 mb-2 justify-between">
    <div :class="`flex flex-col ${leftStacking}:flex-row gap-2 justify-start`">
      <VaButtonToggle
        v-model="sharedFilters.isActive"
        color="background-element"
        class="self-center"
        border-color="background-element"
        :options="[
          { label: info.activeText ?? 'Active', value: true, icon: 'visibility' },
          { label: info.inactiveText ?? 'Inactive', value: false, icon: 'visibility_off' },
        ]"
      />
      <VaInput v-model="sharedFilters.filterString" type="search" placeholder="Search...">
        <template #prependInner>
          <VaIcon class="material-icons" color="secondary"> search </VaIcon>
        </template>
      </VaInput>
      <slot name="left" />
    </div>
    <div class="flex flex-col md:flex-row gap-2 justify-end">
      <slot name="right" />
      <template v-if="filterBy && defaultFilter && !showFilters">
        <FieldValueFilter
          v-model="fieldValue"
          :obj-type="type"
          :field-name="filterBy"
          :value-items="valueList"
          :value-entities="Object.values(externList)"
          clearable
        />
      </template>
      <VaButton gradient color="success" icon="blur_on" class="w-full" @click="showFilters = !showFilters">
        Filters
      </VaButton>
    </div>
  </div>
  <VaCard v-if="showFilters" outlined class="pt-4 pl-4 pr-4 pb-2">
    <div class="flex flex-col sm:flex-row gap-2 mb-2 justify-between">
      <div class="flex flex-col sm:flex-row gap-2 justify-start">
        <VaSelect
          v-model="filterBy"
          :options="makeFields()"
          text-by="text"
          value-by="value"
          placeholder="Filter by"
          searchable
        />
        <FieldValueFilter
          v-if="filterBy"
          v-model="fieldValue"
          :obj-type="type"
          :field-name="filterBy"
          :value-items="valueList"
          :value-entities="Object.values(externList)"
        />
      </div>
      <div class="flex flex-col md:flex-row gap-2 justify-end">
        <VaButton v-if="fieldValue != null || listHasNull" icon="add" gradient class="w-full" @click="onFilterAdd">
          Filter
        </VaButton>
      </div>
    </div>
    <div v-if="Object.keys(filters).length > 0" class="flex flex-col sm:flex-row gap-2 mt-4 mb-2 justify-between">
      <div class="flex flex-col sm:flex-row gap-2 justify-start">
        <template v-for="(value, key) in filters" :key="key">
          <VaChip v-model="filterMirror[key]" closeable size="small" class="h-full w-full text-nowrap" shadow>
            {{ getChip(key, value) }}
          </VaChip>
        </template>
      </div>
    </div>
  </VaCard>
</template>
