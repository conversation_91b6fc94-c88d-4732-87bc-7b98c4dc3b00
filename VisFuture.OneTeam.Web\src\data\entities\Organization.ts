import { Info } from '../FieldTypes'
import { CodeEntity } from './CodeEntity'
import { OrganizationEmployee } from './OrganizationEmployee'
import { OrganizationHierarchy } from './OrganizationHierarchy'
import { AssignableRole } from './AssignableRole'
import { TenantEntityInfo } from './TenantEntity'

export interface Organization extends CodeEntity {
  name: string
  description?: string
  isActive: boolean
  organizationEmployees?: OrganizationEmployee[]
  organizationHierarchies?: OrganizationHierarchy[]
  assignableRoles?: AssignableRole[]
}

export const organizationInfo: Info<Organization> = {
  typeName: 'Organization',
  nameKey: 'name',
  sortKey: 'name',
  backend: 'BaseBiz',
  endpoint: 'Organization',
  fields: {
    code: { label: 'Code', type: 'smalltext', disabled: true },
    name: { label: 'Name', type: 'bigtext', required: true },
    description: { label: 'Description', type: 'textarea' },
    isActive: { label: 'Active', type: 'bool', required: true },
    organizationEmployees: { label: 'Employees', type: 'objlist' },
    organizationHierarchies: { label: 'Hierarchies', type: 'objlist' },
    assignableRoles: { label: 'Assignable Roles', type: 'objlist' },
    ...TenantEntityInfo.fields,
  },
  options: {
    ...TenantEntityInfo.options,
  },
  default: {
    code: 'Auto-generated',
    name: '',
    isActive: true,
  },
  columnsShown: new Set(['code', 'name', 'description', 'isActive']),
  formLayout: [['name', 'code'], ['isActive'], ['description']],
}
