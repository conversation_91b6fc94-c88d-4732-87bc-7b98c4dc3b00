import { useToast } from 'vuestic-ui'
import { useUserStore } from '../../stores/user-store'
import { BaseEntity } from '../../data/entities/BaseEntity'
import { BaseQuery } from './baseQuery'
import { EntityResponse } from './entityResponse'
import { RETRY_LIMIT } from '../../data/Constants'
import { B64File } from '../fileUtils'

const { init: notify } = useToast()
const backends = ['BaseBiz', 'Project', 'Ticket'] as const
export type Backends = (typeof backends)[number]
export const baseUrls: Record<Backends, string> = {
  BaseBiz: import.meta.env.VITE_APP_BASEBIZ_URL as string,
  Project: import.meta.env.VITE_APP_PROJECT_URL as string,
  Ticket: import.meta.env.VITE_APP_TICKET_URL as string,
} as const

/**
 * Sends an authenticated request to the specified backend.
 * @param {Backends} backend - The backend to send the request to.
 * @param {string} endpoint - The endpoint to send the request to.
 * @param {string} method - The HTTP method to use for the request.
 * @param {BaseEntity | BaseQuery | B64File} [body] - The request body.
 * @param {number} [retry=0] - The number of times the request has been retried.
 * @returns {Promise<EntityResponse>} The response from the server.
 */
export async function authedRequest(
  backend: Backends,
  endpoint: string,
  method: string,
  body?: BaseEntity | BaseQuery | B64File,
  retry: number = 0,
): Promise<EntityResponse> {
  if (retry > RETRY_LIMIT) {
    const msg = 'Failed to autherize, retry limit reached'
    notify({
      message: msg,
      color: 'danger',
    })
    throw new Error(msg)
  }
  const store = useUserStore()
  try {
    const res = await fetch(`${baseUrls[backend]}${endpoint}`, {
      method,
      headers: { 'Content-Type': 'application/json', Authorization: `Bearer ${store.jwtToken}` },
      body: JSON.stringify(body),
    })
    if (res.status === 401) {
      // jwt refresh request, currently broken
      const res = await fetch(`${baseUrls[backend]}/refresh`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          jwtToken: store.jwtToken,
          refreshToken: store.refreshToken,
        }),
      })
      const data = await res.json()
      store.jwtToken = data.jwtToken
      store.refreshToken = data.refreshToken
      return await authedRequest(backend, endpoint, method, body, retry + 1)
    } else if (res.status !== 200) {
      throw new Error(`${res.statusText}: ${(await res.json()).message}`)
    }
    const json = (await res.json()) as EntityResponse
    if (!json.isSuccess) {
      json.errors?.forEach((e) => notify({ message: e, color: 'danger' }))
      throw new Error(json.message, { cause: json.errors ?? undefined })
    }
    return json
  } catch (e) {
    notify({
      message: (e as Error).message,
      color: 'danger',
    })
    throw e
  }
}
