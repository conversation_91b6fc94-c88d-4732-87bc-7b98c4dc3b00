import { Info } from '../FieldTypes'
import { BaseEntity, BaseEntityInfo } from './BaseEntity'

export interface TenantEntity extends BaseEntity {
  tenantId?: string
}

export const TenantEntityInfo: Info<TenantEntity> = {
  typeName: 'TenantEntity',
  nameKey: 'id',
  sortKey: 'id',
  backend: 'BaseBiz',
  endpoint: 'Dummy',
  fields: {
    ...BaseEntityInfo.fields,
    tenantId: { label: 'Tenant', type: 'external' },
  },
  options: {
    tenantId: { entity: 'Tenant' },
  },
  default: {},
  columnsShown: new Set(),
  formLayout: [
    ['id', 'tenantId'],
    ['createAt', 'createBy'],
    ['updateAt', 'updateBy'],
  ],
}
