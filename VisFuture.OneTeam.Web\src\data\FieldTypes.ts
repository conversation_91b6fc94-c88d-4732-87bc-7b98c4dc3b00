import { Backends } from '../services/api/requests'
import { BaseEntity } from './entities/BaseEntity'
import { <PERSON>hema<PERSON><PERSON> } from '../data/schema'
import { Ref } from 'vue'

// configuration for each entity
export interface Info<T extends BaseEntity> {
  typeName: string // human-readable name of the entity
  typeShortName?: string // human-readable short name for the entity
  nameKey: Key<T> // key to use for the name of the entity
  sortKey: Key<T> // key to use for initial sorting the entity
  sortAsc?: boolean // whether to sort in ascending order
  hasFiles?: boolean // whether the entity has files
  backend: Backends // which backend to use for the entity
  endpoint: string // which api endpoint to use for the entity
  fields: Record<Key<T>, FieldInfo> // configuration for each field, table order is determined here
  options: Options<T> // extra options for each field
  default: T // default values for creating a new entity (Options<T>)
  columnsShown: Set<Key<T>> // default columns shown in the table
  highlight?: (x: T, extList?: Record<string, Record<string, BaseEntity>>) => boolean // highlight rows in the table
  lowlight?: (x: T, extList?: Record<string, Record<string, BaseEntity>>) => boolean // lowlight rows in the table (make them look duller)
  toggleKey?: Key<T> // key to use for toggling entities
  toggle?: (x: T) => void // function to toggle entities
  formLayout: Key<T>[][] // layout of the form
  formSize?: 'small' | 'medium' | 'large' // size of the (non-tab) form
  defaultFilter?: Key<T> // default filter to automatically show in EntityFilters
  tabList?: TabEntry<T> // list of tabs to show in EntityTabComponent
  activeText?: string // text to show for the active option in EntityFilters
  inactiveText?: string // text to show for the inactive option in EntityFilters
}

// configuration for each field
export interface FieldInfo {
  label: string // human-readable name of the field, as displayed in the form and table headers
  type: (typeof ColumnTypes)[number] // type of the field, which controlls the logic of the field
  required?: boolean // whether the field is required
  disabled?: boolean // whether the field is disabled
}
const ColumnTypes = [
  'smalltext', // fixed length line of text
  'bigtext', // unconstrained / unstructured text body
  'textarea', // text body with multiple lines
  'select', // dropdown list of options
  'code', // sequenceNo codes for refrencing entities (SHOULD NOT be placed on field with name 'code')
  'codetype', // codeType codes for refrencing dynamic dropdowns (SHOULD NOT be placed on field with name 'code')
  'objlist', // list of entities, not yet implemented
  'date', // date only
  'time', // length of hours & minutes
  'datetime', // date and time + hour info
  'number', // number input
  'currency', // numerical currency formatted with currency code stored in another field
  'bool',
  'email',
  'tel',
  'place', // physical location, links to google maps
  'url',
  'external', // displays using the value of another entity in the database
  'percent', // edits like number in form, displays like percent in table
  'badge', // displays as a coloured rectangle
  'file', // file upload
  'filtered', // Filters columns
  'link', // links to another page
] as const

// convienience types
export type Key<T> = keyof T & string
export type BaseKey = keyof BaseEntity & string
export type PRecord<T extends string, U> = Partial<Record<T, U>>
// before
// export type TabEntry<T> = PRecord<SchemaKey, { tabName: string; relation: PRecord<Key<T>, string> }>

// after
export type TabEntry<T> = PRecord<string, { tabName: string; relation: PRecord<Key<T>, string> }>

// options for each field
export interface CurrencyOp<T extends BaseEntity> {
  currencyKey: Key<T>
}
export interface BadgeOp {
  // what string maps to what color (right side are color codes)
  badge: { [value: string]: 'primary' | 'success' | 'danger' | 'warning' | 'secondary' | 'info' }
}
export interface ExternalOp<T extends BaseEntity> {
  entity: SchemaKey // external entity type from schema
  joins?: PRecord<Key<T>, string> // dynamic entity constraints: "extern must have same projectId as this entity"
  constraints?: Record<string, string[]> // static entity constraints: "extern must have color = 'blue' or 'red' (cast to string)"
  extraInfo?: Record<string, Key<T>> // provide extra info for the backend
  valueKey?: string
}
export interface SelectOp {
  options: string[] // static list of options for the dropdown
}
export interface CodeTypeOp {
  typeCode: string // human-readable name of codeType, as stored in the database
}
export interface SyncOp<T extends BaseEntity> {
  sync: (x: Ref<T>, extern?: Ref<BaseEntity>) => void // syncronizes changes in one field to another
}
export interface FileOp {
  fileKey: string // file blob field secretly embedded within the entity
}
export type Options<T extends BaseEntity> = Partial<
  Record<
    Key<T>,
    CurrencyOp<T> | BadgeOp | ExternalOp<T> | SelectOp | CodeTypeOp | SyncOp<T> | FileOp | FilteredOp<T> | LinkOp<T>
  >
> & {
  [key: string]:
    | CurrencyOp<T>
    | BadgeOp
    | ExternalOp<T>
    | SelectOp
    | CodeTypeOp
    | SyncOp<T>
    | FileOp
    | FilteredOp<T>
    | LinkOp<T>
    | undefined
}

export interface FilteredOp<T extends BaseEntity> {
  entity: SchemaKey
  filteredEndpoint?: string // optional, if not provided, will use endpoint + 'List'
  extraInfo?: Record<string, Key<T>>
  valueKey?: string // optional, if not provided, will use nameKey of the entity
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export interface LinkOp<T extends BaseEntity> {
  entity: SchemaKey
  routePrefix?: string
  labelField?: Key<T>
}
