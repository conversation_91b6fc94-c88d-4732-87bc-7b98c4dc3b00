import { Info } from '../FieldTypes'
import { TenantEntity, TenantEntityInfo } from './TenantEntity'
import { Company } from './Company'

export interface ProjectOverview extends TenantEntity {
  projectId: string
  companyId: string
  description?: string
  relatedType: string
  contactName: string
  contactJobTitle?: string
  contactEmail?: string
  contactPhone?: string
  contactFax?: string
  contactMobile?: string
  contactAddress1?: string
  contactAddress2?: string
  contactCity?: string
  contactProvince?: string
  contactPostalCode?: string
  currency?: string
}

export const RelatedTypes = ['Client', 'Vendor', 'Partner', 'Other'] as const
export type RelatedType = (typeof RelatedTypes)[number]

export const projectOverviewInfo: Info<ProjectOverview> = {
  typeName: 'Project Related Party',
  typeShortName: 'Related Party',
  nameKey: 'companyId',
  sortKey: 'relatedType',
  sortAsc: true,
  backend: 'Project',
  endpoint: 'RelatedParty',
  fields: {
    projectId: { label: 'Project', type: 'external', required: true },
    relatedType: { label: 'Related Type', type: 'codetype', required: true },
    companyId: { label: 'Company', type: 'external', required: true },
    contactName: { label: 'Contact Name', type: 'bigtext', required: true },
    contactJobTitle: { label: 'Job Title', type: 'smalltext' },
    contactEmail: { label: 'Email', type: 'email' },
    contactPhone: { label: 'Phone', type: 'tel' },
    contactFax: { label: 'Fax', type: 'tel' },
    contactMobile: { label: 'Mobile', type: 'tel' },
    contactAddress1: { label: 'Address 1', type: 'place' },
    contactAddress2: { label: 'Address 2', type: 'place' },
    contactCity: { label: 'City', type: 'place' },
    contactProvince: { label: 'Province', type: 'codetype' },
    contactPostalCode: { label: 'Postal Code', type: 'place' },
    currency: { label: 'Currency', type: 'codetype' },
    description: { label: 'Description', type: 'textarea' },
    ...TenantEntityInfo.fields,
  },
  options: {
    projectId: { entity: 'Project' },
    companyId: {
      entity: 'Company',
      sync: (x, extern?) => {
        if (!extern?.value) return
        const company = extern.value as Company
        x.value.contactName = company.name
        x.value.contactAddress1 = company.address1
        x.value.contactAddress2 = company.address2
        x.value.contactCity = company.city
        x.value.contactProvince = company.province
        x.value.contactPostalCode = company.postalCode
        x.value.contactFax = company.fax
        x.value.currency = company.currency
        x.value.contactPhone = company.phone
      },
    },
    currency: { typeCode: 'Currency' },
    relatedType: { typeCode: 'RelatedPartyType' },
    contactProvince: { typeCode: 'Province' },
    ...TenantEntityInfo.options,
  },
  default: {
    projectId: '',
    companyId: '',
    relatedType: '',
    contactName: '',
  },
  columnsShown: new Set(),
  formLayout: [
  ],
}

