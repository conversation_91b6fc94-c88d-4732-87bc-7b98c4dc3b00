<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { authedRequest } from '../../services/api/requests'

// Props: the main Ticket being edited in the parent modal
const props = defineProps<{ ticket: any | null }>()
const ticket = computed(() => props.ticket)

// Compose model
const to = ref<string>('')
const cc = ref<string>('')
const subject = ref<string>('')
const bodyHtml = ref<string>('')
const isPublic = ref<boolean>(true)

// Prefill whenever ticket changes
watch(ticket, (t) => {
  if (!t) return
  to.value = t.raisedBy || ''                              // email the ticket "sender"
  subject.value = `Re: ${t.name ?? t.code ?? 'Ticket'}`
  isPublic.value = true
}, { immediate: true })

const canSend = computed(() =>
  !!ticket.value?.id && !!to.value && !!subject.value && !!bodyHtml.value
)

async function sendEmail() {
  if (!canSend.value) return
  await authedRequest('Ticket', '/SendTicketEmail', 'POST', {
    ticketId: ticket.value.id,
    to: to.value,
    cc: cc.value || null,
    subject: subject.value,
    bodyHtml: bodyHtml.value,
    isPublic: isPublic.value,
  })
  // simple reset; the discussion tab will show the new Email entry when user switches back
  bodyHtml.value = ''
}
</script>

<template>
  <div class="space-y-4">
    <VaAlert color="info" class="mb-2">
      This will email the customer and save the message in the Discussion thread.
    </VaAlert>

    <div class="grid md:grid-cols-2 gap-4">
      <VaInput v-model="to" label="To" placeholder="<EMAIL>" />
      <VaInput v-model="cc" label="Cc (optional)" placeholder="<EMAIL>; <EMAIL>" />
    </div>

    <VaInput v-model="subject" label="Subject" />

    <VaTextarea
      v-model="bodyHtml"
      label="Message (HTML allowed)"
      autosize
      placeholder="Type your message…"
    />

    <div class="flex items-center justify-between">
      <VaCheckbox v-model="isPublic" label="Also show in Discussion" />
      <VaButton preset="primary" :disabled="!canSend" @click="sendEmail">Send</VaButton>
    </div>
  </div>
</template>
