// mostly copied from "vuestic-ui/packages/ui/src/components/va-time-input/hooks/time-text-parser.ts"
const parse = (text: string) => {
  const m = text.match(/[0-9]{1,2}/g)

  if (!m) {
    return []
  }

  return m.map((s) => Number(s))
}

const parsePeriod = (text: string) => {
  const m = text.match(/pm|am/i)

  if (!m) {
    return null
  }

  return Number(m[0].toLowerCase() === 'pm')
}

// fix date resetting when time changes
export const parseDateTime = (date: Date) => (text: string) => {
  const [h, m] = parse(text)
  const period = parsePeriod(text)
  const d = new Date(date)

  const is12format = period !== null && h <= 12
  const isPM = is12format && !!period
  // Switch 12 to 0, because of 12h format
  const fh = is12format ? (h === 12 ? 0 : h) : h

  d.setHours(Math.min(fh || 0, is12format ? 12 : 24) + (isPM ? 12 : 0))
  d.setMinutes(Math.min(m || 0, 60))
  d.setSeconds(0)

  return d
}

export const parseDate = (date: Date) => (text: string) => {
  const d: Date = new Date(Date.parse(text))
  d.setHours(date.getHours(), date.getMinutes())
  console.log(d)
  return d
}
