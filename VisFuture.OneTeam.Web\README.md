# OneTeam Pro Frontend

Enterprise project management B2B SAAS solution built with Vue.js, ASP.NET, and MSSQL

### Setup guide

Install the following software:

- [Visual Studio Code](https://code.visualstudio.com/)
- [Node Version Manager](https://github.com/nvm-sh/nvm)
  - Node Package Manager
  - Yarn
- [Vue Devtools](https://chrome.google.com/webstore/detail/vuejs-devtools/nhdogjmejiglipccpnnnanhbledajbpd)

Recommended software:

- [WinGet](https://learn.microsoft.com/en-us/windows/package-manager/winget/)
- [Github Desktop](https://desktop.github.com/)
- [Github Copilot](https://github.com/features/copilot)
- [Augment Code](https://augmentcode.com/)

---

1. Clone the repository: Can be done with Github Desktop or through VSCode's source control tab
2. Install `nvm`, use `nvm` to install `npm`, use `npm` to install `yarn`
3. GIT CHECKOUT TO THE DEV BRANCH!!! I FORGET THIS EVERY TIME!!!
4. Run `yarn` in the root directory to install all dependencies
5. Run `vite` or `yarn run dev` in the root directory to run the project (be sure to start the VPN and backend)
6. Run `yarn build` daily to fix linter errors, ts compile errors, and ensure the project builds correctly
7. Run `yarn run storybook` to run storybook, a frontend testing tool

### Important Files (please read)

- `DOC.md` - Contains project documentation
- `src/data/schema.ts` - Contains set of all entities
- `src/data/entities/*.ts` - Contains all entity interfaces
- `src/data/FieldTypes.ts` - Contains all entity field types
- `src/data/Constants.ts` - Contains all constants
- `src/services/api/*.ts` - Contains all API services
- `src/services/entityPage.ts` - Contains logic for generic entity pages

### Helpful Links

- [Frontend Diagram](https://mayerglobal.sharepoint.com/:u:/s/OneTeamPro/Ee93jMvJUZNKu8wmAeEde18BgngYxY7LC9wrQKmI_EaLdg?e=o47V0H)
- [Vuestic UI Docs](https://ui.vuestic.dev/introduction/overview)
- [Vuestic Admin Github](https://github.com/epicmaxco/vuestic-admin)
- [JS Reference](https://javascript.info)
- [TS Reference](https://www.typescriptlang.org/docs/handbook/2/basic-types.html)
- [Vue Tutorial](https://laracasts.com/series/learn-vue-3-step-by-step)
- [Another Vue Tutorial](https://scrimba.com/learn-vuejs-c020)
- [Tailwind Reference](https://tailwindcss.com/docs/styling-with-utility-classes)
