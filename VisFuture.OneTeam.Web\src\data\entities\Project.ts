import { Info } from '../FieldTypes'
import { CodeEntity } from './CodeEntity'
import { ProjectContract } from './ProjectContract'
import { ProjectRelatedParty } from './ProjectRelatedParty'
import { ProjectRoleAssignment } from './ProjectRoleAssignment'
import { ProjectTask } from './ProjectTask'
import { ProjectTimeLog } from './ProjectTimeLog'
import { ProjectPaymentPlan } from './ProjectPaymentPlan'
import { ProjectPaymentFact } from './ProjectPaymentFact'
import { TenantEntityInfo } from './TenantEntity'
import { ProjectOverview } from './ProjectOverview'

export interface Project extends CodeEntity {
  name: string
  mainType: MainType
  subType?: SubType
  projectManager: string
  description?: string
  startDate: Date
  endDate?: Date
  status: string
  isActive: boolean
  projectContracts?: ProjectContract[]
  projectRelatedParties?: ProjectRelatedParty[]
  projectRoleAssignments?: ProjectRoleAssignment[]
  projectTimeLogs?: ProjectTimeLog[]
  projectTasks?: ProjectTask[]
  projectPaymentPlans?: ProjectPaymentPlan[]
  projectPaymentFacts?: ProjectPaymentFact[]
  projectOverview?: ProjectOverview[]
}

// export const Tabs = [
//   'Summary',
//   'Related Parties',
//   'Contracts',
//   'Team Members',
//   'Tasks',
//   'Tickets',
//   'Payments',
//   'Documents',
// ] as const
export const MainTypes = ['01', '02'] as const
export type MainType = (typeof MainTypes)[number]
export const SubTypes = ['WaterFall', 'Agile'] as const
export type SubType = (typeof SubTypes)[number]
export const Statuses = ['New', 'In Progress', 'Completed'] as const
export type Status = (typeof Statuses)[number]

export const projectInfo: Info<Project> = {
  typeName: 'Project',
  nameKey: 'name',
  sortKey: 'code',
  sortAsc: true,
  backend: 'Project',
  endpoint: 'Project',
  formSize: 'medium',
  fields: {
    code: { label: 'Code', type: 'smalltext', disabled: true },
    name: { label: 'Name', type: 'bigtext', required: true },
    mainType: { label: 'Type', type: 'codetype', required: true },
    subType: { label: 'Sub Type', type: 'select' },
    projectManager: { label: 'Manager', type: 'external', required: true },
    status: { label: 'Status', type: 'codetype', required: true },
    description: { label: 'Description', type: 'textarea' },
    startDate: { label: 'Start Date', type: 'date' },
    endDate: { label: 'End Date', type: 'date' },
    isActive: { label: 'Active', type: 'bool', required: true },
    projectContracts: { label: 'Contracts', type: 'objlist' },
    projectRelatedParties: { label: 'Related Parties', type: 'objlist' },
    projectRoleAssignments: { label: 'Role Assignments', type: 'objlist' },
    projectTimeLogs: { label: 'Time Logs', type: 'objlist' },
    projectTasks: { label: 'Tasks', type: 'objlist' },
    projectPaymentPlans: { label: 'Payment Plans', type: 'objlist' },
    projectPaymentFacts: { label: 'Payment Facts', type: 'objlist' },
    projectOverview: { label: 'Project Overview', type: 'objlist' },
    ...TenantEntityInfo.fields,
  },
  options: {
    mainType: { typeCode: 'ProjectType' },
    subType: { options: SubTypes.slice() },
    status: { typeCode: 'ProjectStatus' },
    projectManager: { entity: 'Employee' },
    projectContracts: { entity: 'ProjectContract' },
    projectRelatedParties: { entity: 'ProjectRelatedParty' },
    projectRoleAssignments: { entity: 'ProjectRoleAssignment' },
    projectTimeLogs: { entity: 'ProjectTimeLog' },
    projectTasks: { entity: 'ProjectTask' },
    projectPaymentPlans: { entity: 'ProjectPaymentPlan' },
    projectPaymentFacts: { entity: 'ProjectPaymentFact' },
    projectOverview: { entity: 'ProjectOverview' },
    ...TenantEntityInfo.options,
  },
  default: {
    code: 'Auto-generated',
    name: '',
    projectManager: '',
    mainType: '01',
    startDate: new Date(),
    status: '01',
    isActive: true,
  },
  columnsShown: new Set(['code', 'name', 'mainType', 'projectManager', 'status', 'startDate', 'endDate', 'isActive']),
  formLayout: [
    ['code', 'name', 'mainType', 'status'],
    ['startDate', 'endDate', 'projectManager', 'isActive'],
    ['description'],
  ],
  tabList: {
    ProjectOverview: { tabName: 'Project Overview', relation: { id: 'projectId' } },
    ProjectRoleAssignment: { tabName: 'Team Members', relation: { id: 'projectId' } },
    ProjectRelatedParty: { tabName: 'Related Parties', relation: { id: 'projectId' } },
    ProjectContract: { tabName: 'Contracts', relation: { id: 'projectId' } },
    ProjectDocument: { tabName: 'Documents', relation: { id: 'projectId' } },
    Ticket: { tabName: 'Tickets', relation: { id: 'projectId' } },
    ProjectTimeLog: { tabName: 'Time Logs', relation: { id: 'projectId' } },
    ProjectTask: { tabName: 'Tasks', relation: { id: 'projectId' } },
    ProjectPaymentPlan: { tabName: 'Payment Plans', relation: { id: 'projectId' } },
    ProjectPaymentFact: { tabName: 'Payment Facts', relation: { id: 'projectId' } },
  },
}
