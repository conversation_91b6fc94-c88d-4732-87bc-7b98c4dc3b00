import { Info } from '../FieldTypes'
import { RoleAccess } from './RoleAccess'
import { TenantEntity, TenantEntityInfo } from './TenantEntity'

export interface AccessResource extends TenantEntity {
  name: string
  resourceCode: string
  description?: string
  isActive: boolean
  isPublic: boolean
  subType: string
  superiorId?: string
  systemId?: string
  moduleId?: string
  roleAccesses?: RoleAccess[]
}

export const AccessResourceSubTypes = ['Menu', 'Page', 'Button', 'Data'] as const
export type AccessResourceSubType = (typeof AccessResourceSubTypes)[number]
export const Systems = ['BaseBiz', 'Project', 'Ticket', 'People'] as const
export type System = (typeof Systems)[number]

export const accessResourceInfo: Info<AccessResource> = {
  typeName: 'Access Resource',
  nameKey: 'name',
  sortKey: 'resourceCode',
  sortAsc: true,
  backend: 'BaseBiz',
  endpoint: 'AccessResource',
  fields: {
    systemId: { label: 'System', type: 'codetype', required: true },
    moduleId: { label: 'Module', type: 'codetype' },
    subType: { label: 'Type', type: 'codetype', required: true },
    resourceCode: { label: 'Code', type: 'smalltext', disabled: true },
    name: { label: 'Name', type: 'bigtext', required: true },
    superiorId: { label: 'Superior', type: 'external' },
    description: { label: 'Description', type: 'textarea' },
    isActive: { label: 'Active', type: 'bool', required: true },
    isPublic: { label: 'Public', type: 'bool', required: true },
    roleAccesses: { label: 'Role Accesses', type: 'external' },
    ...TenantEntityInfo.fields,
  },
  options: {
    superiorId: { entity: 'AccessResource' },
    subType: { typeCode: 'AccessResourceType' },
    systemId: { typeCode: 'System' },
    moduleId: { typeCode: 'Module' },
    ...TenantEntityInfo.options,
  },
  default: {
    resourceCode: 'Auto-generated',
    name: '',
    isActive: true,
    isPublic: false,
    subType: '',
  },
  columnsShown: new Set(['resourceCode', 'name', 'subType', 'superiorId', 'systemId', 'isActive', 'isPublic']),
  formLayout: [
    ['resourceCode', 'name'],
    ['superiorId', 'subType'],
    ['isActive', 'isPublic'],
    ['systemId', 'moduleId'],
    ['description'],
  ],
}
