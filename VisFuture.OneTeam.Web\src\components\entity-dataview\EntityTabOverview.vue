<script setup lang="ts">
import { schema, SchemaKey } from '../../data/schema'
import { computed, ref, watch } from 'vue'
import { getExternMap, isExtern } from '../../services/externUtils'
import EntityTableCell from './EntityTableCell.vue'
import { BaseEntity } from '../../data/entities/BaseEntity'


const props = defineProps<{
    entity: Record<string, any> | null,
    timeLogs?: any[],
    contracts?: any[],
    tasks?: any[],
    tickets?: any[],
    payments?: any[],
    relatedParties?: any[]
}>()

const safePropAccess = <T>(items: T[] | undefined, defaultValue: any[] = []) => items || defaultValue

const minutesToHours = (minutes: number): number => 
    Math.round((minutes || 0) / 60 * 100) / 100

const getPartyName = (partyId: string): string => 
    (props.relatedParties || []).find(p => p.id === partyId)?.contactName || partyId

const formatDateOnly = (dateTime: string | Date): string => {
    if (!dateTime) return 'N/A'
    const date = new Date(dateTime)
    return date.toLocaleDateString()
}

// Calculate business hours between two dates, inclusive
const calculateBusinessHours = (startDate: string | Date, endDate: string | Date): number => {
    if (!startDate || !endDate) return 0;

    const start = new Date(startDate);
    const end = new Date(endDate);

    if (end < start) return 0;

    let businessDays = 0;
    const current = new Date(start);
  
    while (current <= end) {
        const dayOfWeek = current.getDay();
        if (dayOfWeek >= 1 && dayOfWeek <= 5) { 
            businessDays++;
        }
        current.setDate(current.getDate() + 1);
    }

    return Math.max(businessDays, 1) * 8;
}

// Calculate time elapsed between start and end dates as %
const calculateTimeElapsed = (startDate: string | Date, endDate: string | Date): string => {
    if (!startDate || !endDate) return '0%';

    const start = new Date(startDate);
    const end = new Date(endDate);
    const today = new Date();

    if (today < start) return '0%';
    if (today >= end) return '100%';

    const totalDays = Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24));
    const elapsedDays = Math.ceil((today.getTime() - start.getTime()) / (1000 * 60 * 60 * 24));

    const percentage = Math.round((elapsedDays / totalDays) * 100);
    return Math.max(0, Math.min(100, percentage)) + '%';
}

// Set a color based on completion 
const getCompletionColor = (percentage: number): string => {
    if (percentage >= 80) return '#4CAF50'; 
    if (percentage >= 60) return '#FFC107'; 
    if (percentage >= 40) return '#FF9800'; 
    return '#f44336'; 
};

// Compute properties
const projectSummary = computed(() => {
    if (!props.entity) return null

    return {
        startDate: props.entity.startDate,
        endDate: props.entity.endDate,
        projectId: props.entity.id
    }
})

// Calculate total estimated hours from task date ranges
const totalEstimatedHoursFromDates = computed(() => {
    if (!props.tasks?.length) return 0;

    return props.tasks.reduce((sum, task) => {
        if (task.startDate && task.endDate) {
            return sum + calculateBusinessHours(task.startDate, task.endDate);
        }
        return sum + (task.hoursEstimated || 0);
    }, 0);
});

const totalCompletedHoursFromDates = computed(() => {
    if (!props.tasks?.length) return 0;

    return props.tasks.reduce((sum, task) => {
    let completedHours = 0;

    if (task.startDate) {
        const today = new Date();
        const start = new Date(task.startDate);
        
        if (today < start) return sum;
        
        completedHours = calculateBusinessHours(task.startDate, today);
    } else {
        completedHours = task.hoursCompleted || 0;
    }

    let estimatedHours = 0;
    if (task.startDate && task.endDate) {
        estimatedHours = calculateBusinessHours(task.startDate, task.endDate);
    } else {
        estimatedHours = task.hoursEstimated || 0;
    }

    const cappedCompleted = Math.min(completedHours, estimatedHours);

    return sum + cappedCompleted;
    }, 0);
});

// Unified task completion percentage calculation
const taskCompletionPercentage = computed(() => {
    const totalEstimated = totalEstimatedHoursFromDates.value;
    const totalCompleted = totalCompletedHoursFromDates.value;
    return totalEstimated > 0 ? Math.round((totalCompleted / totalEstimated) * 100) : 0;
});

// Group tickets by status
const ticketsByStatus = computed(() => {
    if (!props.tickets?.length) return []

    const groups = new Map<string, any[]>()

    props.tickets.forEach(ticket => {
    const status = ticket.status || 'Unknown'
    if (!groups.has(status)) {
        groups.set(status, [])
    }
        groups.get(status)!.push(ticket)
    })

    return Array.from(groups.entries()).map(([status, tickets]) => ({
        status,
        tickets,
        count: tickets.length,
        totalWorkMinutes: tickets.reduce((sum, t) => sum + (t.workMinutes || 0), 0),
        representativeTicket: { ...tickets[0], status }
    }))
})

// Calculate totals for percentages
const totalTicketCount = computed(() => props.tickets?.length || 0)
const totalWorkMinutes = computed(() => (props.tickets || []).reduce((sum, t) => sum + (t.workMinutes || 0), 0))

// External mapping 
const allLoading = ref(true)
const externList = ref<Record<string, Record<string, BaseEntity>>>({})

const buildExternalMaps = async (entityType: SchemaKey, data: any[], schemaInfo: any) => {
    const tempMaps: Record<string, Record<string, BaseEntity>> = {}
  
    for (const key of Object.keys(schemaInfo.fields)) {
        if (!isExtern(entityType, key)) continue
    
        const idSet = new Set(data.map((x) => x[key])) as Set<string>
        idSet.delete(null as any)
        idSet.delete(undefined as any)
        idSet.delete('' as any)
        
        if (idSet.size > 0) {
        tempMaps[key] = await getExternMap([...idSet], entityType, key)
        }
    }
  
  return tempMaps
}

const makeExternList = async () => {
    const tempList: typeof externList.value = {}

    if (props.payments?.length) {
        Object.assign(tempList, await buildExternalMaps('ProjectPaymentFact', props.payments, schema.ProjectPaymentFact))
    }

    if (props.tickets?.length) {
        Object.assign(tempList, await buildExternalMaps('Ticket', props.tickets, schema.Ticket))
    }

    externList.value = tempList
    allLoading.value = false
}

// Watch for changes and rebuild external list
watch([() => props.tickets, () => props.payments], async () => {
    if ((props.tickets && props.tickets.length > 0) || (props.payments && props.payments.length > 0)) {
        allLoading.value = true
        await makeExternList()
    }
}, { immediate: true })
</script>

<template>
    <div v-if="allLoading && (props.tickets?.length || 0) > 0">
        <p>Loading data mappings...</p>
    </div>

    <div v-else class="overview-box">
        <!-- Progress -->
        <div class="overview-section-box">
            <div class="overview-section-header">
                <div class="overview-completion-circle">
                    <svg width="60" height="60" viewBox="0 0 60 60">
                        <circle
                            cx="30"
                            cy="30"
                            r="25"
                            fill="none"
                            stroke="#e0e0e0"
                            stroke-width="4"
                        />
                        <circle
                            cx="30"
                            cy="30"
                            r="25"
                            fill="none"
                            :stroke="getCompletionColor(taskCompletionPercentage)"
                            stroke-width="4"
                            stroke-linecap="round"
                            :stroke-dasharray="157.08" 
                            :stroke-dashoffset="157.08 - (157.08 * taskCompletionPercentage / 100)"
                            transform="rotate(-90 30 30)"
                        />
                        <text
                            x="30"
                            y="35"
                            text-anchor="middle"
                            font-size="12"
                            font-weight="bold"
                            fill="#333"
                            >
                            {{ taskCompletionPercentage }}%
                        </text>
                    </svg>
                </div>
                <span class="overview-section-title">Progress</span>
            </div>
            <table class="overview-section-table">
                <thead>
                    <tr>
                        <th>Start Date</th>
                        <th>End Date</th>
                        <th>Time Elapsed</th>
                        <th>Tasks Completed</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-if="projectSummary">
                        <td>{{ formatDateOnly(projectSummary.startDate) }}</td>
                        <td>{{ formatDateOnly(projectSummary.endDate) }}</td>
                        <td>{{ calculateTimeElapsed(projectSummary.startDate, projectSummary.endDate) }}</td>
                        <td>{{ taskCompletionPercentage }}%</td>
                    </tr>
                    <tr v-else>
                        <td colspan="4">No project data found</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- Contract -->
        <div class="overview-section-box">
            <div class="overview-section-header">
                <span class="overview-section-title">Contract</span>
            </div>
            <table class="overview-section-table">
                <thead>
                    <tr>
                        <th>Party A</th>
                        <th>Party B</th>
                        <th>Type</th>
                        <th>Effective Date</th>
                        <th>Currency</th>
                        <th>Amount</th>
                        <th>Tax</th>
                        <th>Total</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-for="contract in safePropAccess(props.contracts)" :key="contract.id">
                        <td>{{ getPartyName(contract.relatedPartyA) }}</td>
                        <td>{{ getPartyName(contract.relatedPartyB) }}</td>
                        <td>{{ contract.mainType }}</td>
                        <td>{{ contract.effectiveDate }}</td>
                        <td>{{ contract.currency }}</td>
                        <td>{{ contract.amount }}</td>
                        <td>{{ contract.taxRate }}%</td>
                        <td>{{ contract.totalAmount }}</td>
                    </tr>
                    <tr>
                        <td colspan="6"></td>
                        <td>Sub Total</td>
                        <td>{{ safePropAccess(props.contracts).reduce((sum, c) => sum + (c.totalAmount || 0), 0) }}</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- Task -->
        <div class="overview-section-box">
            <div class="overview-section-header">
                <span class="overview-section-title">Task</span>
            </div>
            <table class="overview-section-table">
                <thead>
                    <tr>
                        <th>Count</th>
                        <th>Hours Estimated</th>
                        <th>Hours Completed</th>
                        <th>% of Completed</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-if="props.tasks && props.tasks.length">
                        <td>{{ props.tasks.length }}</td>
                        <td>{{ totalEstimatedHoursFromDates }}</td>
                        <td>{{ totalCompletedHoursFromDates }}</td>
                        <td>{{ taskCompletionPercentage }}%</td>
                    </tr>
                    <tr v-else>
                        <td colspan="4">No tasks found</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- Ticket -->
        <div class="overview-section-box">
            <div class="overview-section-header">
                <span class="overview-section-title">Ticket</span>
            </div>
            <table class="overview-section-table">
                <thead>
                <tr>
                    <th>Status</th>
                    <th>Count</th>
                    <th>% of Count</th>
                    <th>Time Log Hours</th>
                    <th>% of Hours</th>
                </tr>
                </thead>
                <tbody>
                    <tr v-for="statusGroup in ticketsByStatus" :key="statusGroup.status">
                        <td>
                            <EntityTableCell
                                :obj-key="'status' as any"
                                type="Ticket"
                                :row-data="statusGroup.representativeTicket"
                                :field="schema.Ticket.fields.status"
                                :extern-list="externList"
                            />
                        </td>
                        <td>{{ statusGroup.count }}</td>
                        <td>{{ totalTicketCount > 0 ? Math.round((statusGroup.count / totalTicketCount) * 100) : 0 }}%</td>
                        <td>{{ minutesToHours(statusGroup.totalWorkMinutes) }}</td>
                        <td>{{ totalWorkMinutes > 0 ? Math.round((statusGroup.totalWorkMinutes / totalWorkMinutes) * 100) : 0 }}%</td>
                    </tr>
                    <tr v-if="!props.tickets || !props.tickets.length">
                        <td colspan="5">No tickets found</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- Payment -->
        <div class="overview-section-box">
            <div class="overview-section-header">
                <span class="overview-section-title">Payment</span>
            </div>
            <table class="overview-section-table">
                <thead>
                    <tr>
                        <th>From</th>
                        <th>To</th>
                        <th>Type</th>
                        <th>Transaction Date</th>
                        <th>Currency</th>
                        <th>Amount</th>
                        <th>Tax</th>
                        <th>Total</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-for="payment in safePropAccess(props.payments)" :key="payment.id">
                        <td>
                            <EntityTableCell
                                :obj-key="'payerId' as any"
                                type="ProjectPaymentFact"
                                :row-data="payment"
                                :field="schema.ProjectPaymentFact?.fields?.payerId"
                                :extern-list="externList"
                            />
                        </td>
                        <td>
                            <EntityTableCell
                                :obj-key="'payeeId' as any"
                                type="ProjectPaymentFact"
                                :row-data="payment"
                                :field="schema.ProjectPaymentFact?.fields?.payeeId"
                                :extern-list="externList"
                            />
                        </td>         
                        <td>
                            <EntityTableCell
                                :obj-key="'category' as any"
                                type="ProjectPaymentFact"
                                :row-data="payment"
                                :field="schema.ProjectPaymentFact?.fields?.category"
                                :extern-list="externList"
                            />
                        </td>
                        <td>{{ payment.payDate || payment.transactionDate }}</td>
                        <td>{{ payment.currency }}</td>
                        <td>{{ payment.amount }}</td>
                        <td>{{ payment.taxAmount || payment.tax }}</td>
                        <td>{{ payment.totalAmount || payment.total }}</td>
                    </tr>
                    <tr v-if="!props.payments || !props.payments.length">
                        <td colspan="8">No payments found</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</template>
