<script setup lang="ts">
import { validators } from '../../services/utils'
import { Ref } from 'vue'
import { schema, SchemaKey } from '../../data/schema'
import { BaseEntity } from '../../data/entities/BaseEntity'
import { getCodeItemArray } from '../../services/externUtils'
import { BaseKey } from '../../data/FieldTypes'
import { CodeItem } from '../../data/entities/CodeItem'

const props = defineProps<{
  typeCode: string
  fromType: SchemaKey
  fromKey: string
  required?: boolean
  inline?: boolean
  disabled?: boolean
}>()

const newEntity = defineModel({ required: true }) as Ref<BaseEntity>
const selected = defineModel('selected') as Ref<CodeItem>

const items = (await getCodeItemArray(props.typeCode)).sort((x, y) => (x.seqNo ?? '').localeCompare(y.seqNo ?? ''))
const field = schema[props.fromType].fields[props.fromKey]
const label = props.inline ? '' : field.label + (field.type !== 'bool' && field.required ? ' *' : '')
const _keyName = props.fromKey as BaseKey
if (!newEntity.value[_keyName] && props.required) {
  ;(newEntity.value[_keyName] as string) = items[0].value ?? '' // autofill first codeitem
}
</script>
<template>
  <VaSelect
    v-model="newEntity[_keyName]"
    :label="label"
    :options="items"
    text-by="name"
    :value-by="
      (x) => {
        selected = x as CodeItem
        return (x as CodeItem).value
      }
    "
    :rules="required ? [validators.required] : []"
    :name="fromKey"
    :clearable="!required"
    :searchable="items.length >= 3"
    :disabled="disabled"
  />
</template>
