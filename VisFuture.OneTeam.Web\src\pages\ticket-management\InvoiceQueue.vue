<script setup lang="ts">
import { Ref, ref } from 'vue'
import { schema } from '../../data/schema'
import { UseApi } from '../../services/api/useApi'
import { BaseEntity } from '../../data/entities/BaseEntity'

import EntityComponent from '../../components/entity-dataview/EntityComponent.vue'
import ExternalSelect from '../../components/entity-dataview/ExternalSelect.vue'

const locale = navigator.language
const startDate = ref() as Ref<Date | undefined>
const endDate = ref() as Ref<Date | undefined>
const projContainer = ref({ projectId: null } as any)
const status = ref('All')
const billable = ref('All')

const statusArray = ['All', 'Ready', 'Pre-selected', 'Invoiced']
const billableFilters = {
  Yes: true,
  No: false,
  All: null,
} as Record<string, boolean | null>

const api = await new UseApi<BaseEntity>('ProjectTimeLog').build()

const search = () => {
  const projQuery = projContainer.value.projectId
    ? { projectId: new Set([projContainer.value.projectId]) }
    : ({} as any)
  api.filters.value = {
    fieldValues: {
      ...projQuery,
    },
    startDate: startDate.value,
    endDate: endDate.value,
    billable: billableFilters[billable.value],
    status: status.value,
  } as any
}
const clear = () => {
  api.filters.value = {}
}
</script>

<template>
  <EntityComponent type="ProjectTimeLog" :api="api">
    <template #Q2>
      <div class="flex flex-col md:flex-row gap-2 justify-around">
        <div class="flex flex-col gap-2 mr-auto">
          <h2>
            Date from
            <VaDateInput
              v-model="startDate"
              placeholder="Enter Start Date..."
              :format="(t) => (t as Date).toLocaleDateString(locale)"
              manual-input
              clearable
            />
            to
            <VaDateInput
              v-model="endDate"
              placeholder="Enter End Date..."
              :format="(t) => (t as Date).toLocaleDateString(locale)"
              manual-input
              clearable
            />
          </h2>
          <h2>
            Project:
            <ExternalSelect
              v-model="projContainer"
              class="w-64"
              placeholder="Select Project..."
              from-type="ProjectTimeLog"
              from-key="projectId"
              :option="schema['ProjectTimeLog'].options['projectId']"
              :inline="true"
            />
          </h2>
        </div>
        <div class="flex flex-col gap-2 mr-auto">
          <h2>
            Time Log Status:
            <VaRadio v-model="status" :options="statusArray" />
          </h2>
          <h2>
            Billable Status:
            <VaRadio v-model="billable" :options="Object.keys(billableFilters)" />
          </h2>
        </div>
      </div>
      <div class="flex flex-row gap-2 justify-start mt-4 mb-4">
        <VaButton class="w-fit" gradient color="info" @click="search"> Search </VaButton>
        <VaButton class="w-fit" gradient color="secondary" @click="clear"> clear </VaButton>
      </div>
    </template>
  </EntityComponent>
</template>
