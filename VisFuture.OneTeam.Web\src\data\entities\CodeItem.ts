import { Info } from '../FieldTypes'
import { TenantEntity, TenantEntityInfo } from './TenantEntity'
import { CodeItemAttribute } from './CodeItemAttribute'

export interface CodeItem extends TenantEntity {
  codeTypeId: string
  name: string
  value: string
  seqNo?: string
  superiorId?: string
  itemField1?: string
  itemField2?: string
  itemField3?: string
  codeItemAttributes?: CodeItemAttribute[]
}

export const codeItemInfo: Info<CodeItem> = {
  typeName: 'Code Item',
  nameKey: 'name',
  sortKey: 'seqNo',
  sortAsc: true,
  backend: 'BaseBiz',
  endpoint: 'CodeItem',
  fields: {
    codeTypeId: { label: 'Code Type', type: 'external', required: true },
    name: { label: 'Name', type: 'bigtext', required: true },
    value: { label: 'Value', type: 'smalltext', required: true, disabled: true },
    seqNo: { label: 'Sequence No', type: 'smalltext' },
    superiorId: { label: 'Superior Item', type: 'external' },
    itemField1: { label: 'Field 1', type: 'smalltext' },
    itemField2: { label: 'Field 2', type: 'smalltext' },
    itemField3: { label: 'Field 3', type: 'smalltext' },
    codeItemAttributes: { label: 'Attributes', type: 'external' },
    ...TenantEntityInfo.fields,
  },
  options: {
    superiorId: { entity: 'CodeItem', extraInfo: { underSuperiorOf: 'codeTypeId' } },
    codeTypeId: { entity: 'CodeType' },
    codeItemAttributes: { entity: 'CodeItemAttribute' },
    ...TenantEntityInfo.options,
  },
  default: {
    codeTypeId: '',
    name: '',
    value: '',
  },
  columnsShown: new Set(['codeTypeId', 'name', 'value', 'seqNo', 'superiorId']),
  formLayout: [
    ['codeTypeId', 'seqNo'],
    ['name', 'value'],
    ['superiorId', 'itemField1'],
    ['itemField2', 'itemField3'],
  ],
}
