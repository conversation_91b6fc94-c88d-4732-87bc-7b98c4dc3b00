@import 'vuestic-sass/index';
@import 'icon-fonts/index';

@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  @apply text-regularMedium;

  // TODO Move to some other place so that it's more elegant.
  --va-checkbox-font-size: 0.875rem;
  --va-card-box-shadow: none; // TODO Remove after https://github.com/epicmaxco/vuestic-ui/issues/3964
  --va-card-padding: 1rem;
  --va-font-family: 'Inter', sans-serif;
}

code,
kbd,
samp,
pre {
  font-family: monospace;
  font-size: 1em;
}

.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.page-title {
  @apply text-[32px] md:text-5xl font-bold leading-9 md:leading-[56px] max-sm:mt-6 mb-6 md:mb-4;
}

.h1 {
  @apply text-[32px] md:text-5xl font-bold leading-9 md:leading-[56px] max-sm:mt-6 mb-6 md:mb-4;
}

.h3,
.page-sub-title {
  @apply text-2xl font-bold leading-[30px];
}

.h5 {
  @apply font-bold leading-tight;
}

.block-title {
  @apply text-2xl font-bold mb-2;
}

.pricing-plan-card-title {
  @apply text-[28px] md:text-[32px] leading-10 font-bold;
}

.text-regular-small {
  font-size: 0.8125rem;
  line-height: 1rem;
}

.overview-box {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.overview-section-box {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 1rem;
  background: #fff;
}

.overview-section-header {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
}

.overview-section-title {
  font-size: 1.3rem;
  font-weight: bold;
}

.overview-section-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 0.5rem;
}

.overview-section-table th,
.overview-section-table td {
  padding: 0.4rem 0.6rem;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.overview-section-table th {
  font-weight: 600;
}

.overview-completion-circle {
  width: 60px;
  height: 60px;
  margin-right: 1rem;
}

.overview-completion-circle circle {
  transition: stroke-dashoffset 0.3s ease;
}