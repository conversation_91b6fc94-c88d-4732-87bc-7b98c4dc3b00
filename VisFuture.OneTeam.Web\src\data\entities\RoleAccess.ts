import { Info } from '../FieldTypes'
import { TenantEntity, TenantEntityInfo } from './TenantEntity'

export interface RoleAccess extends TenantEntity {
  roleId: string
  resourceId: string
  accessType: AccessType
}

export const AccessTypes = ['Full', 'Read', 'Write'] as const
export type AccessType = (typeof AccessTypes)[number]

export const roleAccessInfo: Info<RoleAccess> = {
  typeName: 'Role Access',
  nameKey: 'resourceId',
  sortKey: 'roleId',
  sortAsc: true,
  backend: 'BaseBiz',
  endpoint: 'RoleAccess',
  fields: {
    roleId: { label: 'Role', type: 'external', required: true },
    resourceId: { label: 'Resource', type: 'external', required: true },
    accessType: { label: 'Access Type', type: 'codetype', required: true },
    ...TenantEntityInfo.fields,
  },
  options: {
    roleId: { entity: 'Role' },
    resourceId: { entity: 'AccessResource' },
    accessType: { typeCode: 'AccessType' },
    ...TenantEntityInfo.options,
  },
  default: {
    roleId: '',
    resourceId: '',
    accessType: 'Full',
  },
  columnsShown: new Set(['roleId', 'resourceId', 'accessType']),
  formLayout: [['roleId', 'resourceId', 'accessType']],
}
