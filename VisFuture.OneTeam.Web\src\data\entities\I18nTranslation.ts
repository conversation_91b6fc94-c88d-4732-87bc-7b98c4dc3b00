import { Info } from '../FieldTypes'
import { TenantEntity, TenantEntityInfo } from './TenantEntity'

export interface I18nTranslation extends TenantEntity {
  keyId: string
  locale: string
  translation?: string
}

export const Locales = ['en', 'fr', 'es'] as const
export type Locale = (typeof Locales)[number]

export const i18nTranslationInfo: Info<I18nTranslation> = {
  typeName: 'I18n Translation',
  nameKey: 'locale',
  sortKey: 'locale',
  backend: 'BaseBiz',
  endpoint: 'I18nTranslation',
  fields: {
    keyId: { label: 'I18n Key', type: 'external', required: true },
    locale: { label: 'Locale', type: 'codetype', required: true },
    translation: { label: 'Translation', type: 'textarea' },
    ...TenantEntityInfo.fields,
  },
  options: {
    keyId: { entity: 'I18nKey' },
    locale: { typeCode: 'Locale' },
    ...TenantEntityInfo.options,
  },
  default: {
    keyId: '',
    locale: '',
    translation: '',
  },
  columnsShown: new Set(['keyId', 'locale', 'translation']),
  formLayout: [['keyId', 'locale'], ['translation']],
}
