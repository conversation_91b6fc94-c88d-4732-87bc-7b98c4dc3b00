import { Info } from '../FieldTypes'
import { BaseEntity, BaseEntityInfo } from './BaseEntity'

export interface Tenant extends BaseEntity {
  name: string
  description?: string
  address1?: string
  address2?: string
  city?: string
  province?: string
  postalCode?: string
  effectiveDate: Date
  expireDate?: Date
  isActive: boolean
  domain: string
  language?: string
  timeZone?: string
  contactName?: string
  contactPhone?: string
  contactFax?: string
  contactEmail?: string
}

export const tenantInfo: Info<Tenant> = {
  typeName: 'Tenant',
  nameKey: 'name',
  sortKey: 'name',
  sortAsc: true,
  backend: 'BaseBiz',
  endpoint: 'Tenant',
  fields: {
    name: { label: 'Full Name', type: 'bigtext', required: true },
    description: { label: 'Description', type: 'textarea' },
    address1: { label: 'Address1', type: 'place' },
    address2: { label: 'Address2', type: 'place' },
    city: { label: 'City', type: 'place' },
    province: { label: 'Province', type: 'codetype' },
    postalCode: { label: 'Postal Code', type: 'place' },
    effectiveDate: { label: 'Effective Date', type: 'date', required: true },
    expireDate: { label: 'Expire Date', type: 'date' },
    isActive: { label: 'Is Active', type: 'bool', required: true },
    domain: { label: 'Domain', type: 'smalltext', required: true },
    language: { label: 'Language', type: 'codetype' },
    timeZone: { label: 'Time Zone', type: 'codetype' },
    contactName: { label: 'Contact Name', type: 'bigtext' },
    contactPhone: { label: 'Contact Phone', type: 'smalltext' },
    contactFax: { label: 'Contact Fax', type: 'smalltext' },
    contactEmail: { label: 'Contact Email', type: 'smalltext' },
    ...BaseEntityInfo.fields,
  },
  default: {
    name: '',
    effectiveDate: new Date(),
    isActive: true,
    domain: '',
    language: 'en_US',
    timeZone: 'ET',
  },
  options: {
    province: { typeCode: 'Province' },
    language: { typeCode: 'Locale' },
    timeZone: { typeCode: 'TimeZone' },
  },
  columnsShown: new Set([
    'name',
    'effectiveDate',
    'expireDate',
    'isActive',
    'domain',
    'contactName',
    'contactPhone',
    'contactEmail',
  ]),
  formLayout: [
    ['name', 'contactName'],
    ['contactPhone', 'contactEmail'],
    ['effectiveDate', 'expireDate'],
    ['domain'],
    ['address1', 'address2'],
    ['city', 'province', 'postalCode'],
    ['language', 'timeZone'],
    ['isActive'],
    ['description'],
  ],
}
