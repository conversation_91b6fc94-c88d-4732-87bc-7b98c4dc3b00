import { createRouter, create<PERSON>eb<PERSON><PERSON>ory, RouteRecordRaw } from 'vue-router'
import i18n from '../i18n'
import { useUserStore } from '../stores/user-store'

import AuthLayout from '../layouts/AuthLayout.vue'
import AppLayout from '../layouts/AppLayout.vue'

import RouteViewComponent from '../layouts/RouterBypass.vue'
import { nextTick } from 'vue'

const routes: Array<RouteRecordRaw> = [
  {
    path: '/:pathMatch(.*)*',
    redirect: { name: 'dashboard' },
  },
  {
    name: 'global-admin',
    path: '/global-admin',
    redirect: { name: 'manage-global-admins' },
    component: AppLayout,
    children: [
      {
        name: 'global-admin-account',
        path: 'global-admin-account',
        component: () => import('../pages/global-admin/ManageGlobalAdmins.vue'),
      },
      {
        name: 'access-resource',
        path: 'access-resource',
        component: () => import('../pages/global-admin/ResourceAccess.vue'),
      },
      {
        name: 'manage-tenant',
        path: 'manage-tenant',
        component: () => import('../pages/global-admin/Tenants.vue'),
      },
      {
        name: 'tenant-admins',
        path: 'tenant-admins',
        component: () => import('../pages/global-admin/TenantAdmins.vue'),
      },
      {
        name: 'normal-user-account',
        path: 'normal-user-account',
        component: () => import('../pages/tenant-admin/UserAccounts.vue'),
      },
    ],
  },
  {
    name: 'user',
    path: '/',
    component: AppLayout,
    redirect: { name: 'dashboard' },
    children: [
      {
        name: 'dashboard',
        path: 'dashboard',
        component: () => import('../pages/admin/dashboard/Dashboard.vue'),
      },
      {
        name: 'settings',
        path: 'settings',
        component: () => import('../pages/settings/Settings.vue'),
      },
      {
        name: 'preferences',
        path: 'preferences',
        component: () => import('../pages/preferences/Preferences.vue'),
      },
      {
        name: 'payments',
        path: '/payments',
        component: RouteViewComponent,
        children: [
          {
            name: 'payment-methods',
            path: 'payment-methods',
            component: () => import('../pages/payments/PaymentsPage.vue'),
          },
          {
            name: 'billing',
            path: 'billing',
            component: () => import('../pages/billing/BillingPage.vue'),
          },
          {
            name: 'pricing-plans',
            path: 'pricing-plans',
            component: () => import('../pages/pricing-plans/PricingPlans.vue'),
          },
        ],
      },
      {
        name: 'faq',
        path: '/faq',
        component: () => import('../pages/faq/FaqPage.vue'),
      },
    ],
  },
  {
    name: 'tenant-admin',
    path: '/tenant-admin',
    component: AppLayout,
    children: [
      {
        name: 'tenant-settings',
        path: 'tenant-settings',
        component: () => import('../pages/tenant-admin/TenantSettings.vue'),
      },
      {
        name: 'manage-company',
        path: 'manage-company',
        component: () => import('../pages/project-management/ManageCompanies.vue'),
      },
      {
        name: 'tenant-roles',
        path: 'tenant-roles',
        component: () => import('../pages/tenant-admin/TenantRoles.vue'),
      },
      {
        name: 'organizational-hierarchy',
        path: 'organizational-hierarchy',
        component: () => import('../pages/tenant-admin/OrganizationalHierarchy.vue'),
      },
      {
        path: '/tenant-admin/organizational-hierarchy/:id?',
        name: 'OrganizationHierarchy',
        component: () => import('../pages/tenant-admin/OrganizationalHierarchy.vue'),
      },
      {
        name: 'tenant-employees',
        path: 'tenant-employees',
        component: () => import('../pages/tenant-admin/TenantEmployees.vue'),
      },
      {
        name: 'organization-employees',
        path: 'organization-employees',
        component: () => import('../pages/tenant-admin/OrganizationEmployee.vue'),
      },
      {
        name: 'assignable-roles',
        path: 'assignable-roles',
        component: () => import('../pages/tenant-admin/AssignableRoles.vue'),
      },
      {
        name: 'role-access',
        path: 'role-access',
        component: () => import('../pages/tenant-admin/RoleAccess.vue'),
      },
      {
        name: 'role-assignments',
        path: 'role-assignments',
        component: () => import('../pages/tenant-admin/RoleAssignments.vue'),
      },
      {
        name: 'code-types',
        path: 'code-types',
        component: () => import('../pages/tenant-admin/CodeType.vue'),
      },
      {
        name: 'i18n-keys',
        path: 'i18n-keys',
        component: () => import('../pages/tenant-admin/I18nKey.vue'),
      },
      {
        name: 'i18n-translations',
        path: 'i18n-translations',
        component: () => import('../pages/tenant-admin/I18nTranslation.vue'),
      },
      {
        name: 'notification-templates',
        path: 'notification-templates',
        component: () => import('../pages/tenant-admin/NotificationTemplate.vue'),
      },
      {
        name: 'sequence-numbers',
        path: 'sequence-numbers',
        component: () => import('../pages/tenant-admin/SequenceNo.vue'),
      },
    ],
  },
  {
    name: 'project-management',
    path: '/project-management',
    component: AppLayout,
    children: [
      {
        name: 'manage-contact',
        path: 'manage-contact',
        component: () => import('../pages/project-management/CompanyContacts.vue'),
      },
      {
        name: 'manage-project',
        path: 'manage-project',
        component: () => import('../pages/project-management/ProjectsOverview.vue'),
      },
      {
        name: 'manage-contract',
        path: 'manage-contract',
        component: () => import('../pages/project-management/ProjectContracts.vue'),
      },
      {
        name: 'manage-task',
        path: 'manage-task',
        component: () => import('../pages/project-management/ProjectTasks.vue'),
      },
      {
        name: 'manage-document',
        path: 'manage-document',
        component: () => import('../pages/project-management/ProjectDocuments.vue'),
      },
    ],
  },
  {
    name: 'ticket-management',
    path: '/ticket-management',
    component: AppLayout,
    children: [
      {
        name: 'manage-ticket',
        path: 'manage-ticket',
        component: () => import('../pages/ticket-management/ManageTickets.vue'),
      },
      {
        name: 'review-ticket',
        path: 'review-ticket',
        component: () => import('../pages/ticket-management/ReviewTickets.vue'),
      },
      {
        name: 'linked-ticket',
        path: 'linked-ticket',
        component: () => import('../pages/ticket-management/LinkedTickets.vue'),
      },
      {
        name: 'devops-ticket',
        path: 'devops-ticket',
        component: () => import('../pages/ticket-management/DevopsTickets.vue'),
      },
      {
        name: 'ticket-discussion',
        path: 'ticket-discussion',
        component: () => import('../pages/ticket-management/TicketDiscussions.vue'),
      },
      {
        name: 'ticket-attachment',
        path: 'ticket-attachment',
        component: () => import('../pages/ticket-management/TicketAttachments.vue'),
      },
      {
        name: 'convert-email-to-ticket',
        path: 'convert-email-to-ticket',
        component: () => import('../pages/ticket-management/ConvertEmailToTicket.vue'),
      },
      {
        name: 'shopping-list',
        path: 'shopping-list',
        component: () => import('../pages/ticket-management/ShoppingList.vue'),
      },
      {
        name: 'manage-time-log',
        path: 'manage-time-log',
        component: () => import('../pages/ticket-management/TimeLogs.vue'),
      },
      {
        name: 'manage-invoice',
        path: 'manage-invoice',
        component: () => import('../pages/ticket-management/TicketInvoices.vue'),
      },
      {
        name: 'invoice-queue',
        path: 'invoice-queue',
        component: () => import('../pages/ticket-management/InvoiceQueue.vue'),
      },
    ],
  },
  {
    name: 'product-management',
    path: '/product-management',
    component: AppLayout,
    children: [
      {
        name: 'manage-product',
        path: 'manage-product',
        component: () => import('../pages/product-management/ManageProducts.vue'),
      },
      {
        name: 'release-tracking',
        path: 'release-tracking',
        component: () => import('../pages/product-management/ReleaseTracking.vue'),
      },
      {
        name: 'license-tracking',
        path: 'license-tracking',
        component: () => import('../pages/product-management/LicenseTracking.vue'),
      },
      {
        name: 'deployment-tracking',
        path: 'deployment-tracking',
        component: () => import('../pages/product-management/DeploymentTracking.vue'),
      },
    ],
  },
  {
    path: '/auth',
    component: AuthLayout,
    children: [
      {
        name: 'login',
        path: 'login',
        component: () => import('../pages/auth/Login.vue'),
      },
      {
        name: 'signup',
        path: 'signup',
        component: () => import('../pages/auth/Signup.vue'),
      },
      {
        name: 'reset-password',
        path: 'reset-password',
        component: () => import('../pages/auth/ResetPassword.vue'),
      },
      {
        name: 'reset-password-email',
        path: 'reset-password-email',
        component: () => import('../pages/auth/CheckTheEmail.vue'),
      },
    ],
  },
  {
    name: '404',
    path: '/404',
    component: () => import('../pages/404.vue'),
  },
]

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    }
    // For some reason using documentation example doesn't scroll on page navigation.
    if (to.hash) {
      return { el: to.hash, behavior: 'smooth' }
    } else {
      window.scrollTo(0, 0)
    }
  },
  routes,
})
router.afterEach((to) => {
  nextTick(() => {
    document.title = i18n.global.t(('menu.' + to.name?.toString()) as string) + ' | OneTeam'
  })
})

router.beforeEach((to, from, next) => {
  const store = useUserStore()
  const isLoggedIn = !!store.jwtToken && !!store.userName // <- check both
  const isPublicPage = ['login', 'signup', 'reset-password', 'reset-password-email'].includes(to.name as string)

  if (!isLoggedIn && !isPublicPage) {
    next({ name: 'login' }) //  redirect to login
  } else {
    next() //  allow access
  }
})

export default router
