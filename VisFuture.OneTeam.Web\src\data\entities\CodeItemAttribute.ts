import { Info } from '../FieldTypes'
import { CodeEntity } from './CodeEntity'
import { TenantEntityInfo } from './TenantEntity'

export interface CodeItemAttribute extends CodeEntity {
  codeItemId: string
  value: string
}

export const codeItemAttributeInfo: Info<CodeItemAttribute> = {
  typeName: 'Code Item Attribute',
  nameKey: 'code',
  sortKey: 'code',
  backend: 'BaseBiz',
  endpoint: 'CodeItemAttribute',
  fields: {
    code: { label: 'Code', type: 'smalltext', disabled: true },
    codeItemId: { label: 'Code Item', type: 'external', required: true },
    value: { label: 'Attribute Value', type: 'smalltext', required: true },
    ...TenantEntityInfo.fields,
  },
  options: {
    ...TenantEntityInfo.options,
  },
  default: {
    codeItemId: '',
    code: 'Auto-generated',
    value: '',
  },
  columnsShown: new Set(['code', 'value']),
  formLayout: [['codeItemId', 'code'], ['value']],
}
