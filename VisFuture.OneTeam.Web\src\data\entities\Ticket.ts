import { Info } from '../FieldTypes'
import { CodeEntity } from './CodeEntity'
import { TenantEntityInfo } from './TenantEntity'
import { TicketBilling } from './TicketBilling'
import { TicketDevOpsLink } from './TicketDevOpsLink'
import { TicketDiscussion } from './TicketDiscussion'
import { TicketLink } from './TicketLink'
import { TicketReview } from './TicketReview'

export interface Ticket extends CodeEntity {
  projectId: string
  name: string
  description?: string
  mainType: string
  subType?: string
  status: string
  priority: string
  source: string
  sourceId?: string
  raisedOn: Date
  raisedBy?: string
  endDate?: Date
  assignee?: string
  customerId?: string
  contractId?: string
  reviewFlag?: string
  category?: Category
  module?: string
  isPublic: boolean
  billMethod?: string
  billable: boolean
  workMinutes: number // total time logged on this ticket
  ticketBillings?: TicketBilling[]
  ticketDevOpsLinks?: TicketDevOpsLink[]
  ticketDiscussions?: TicketDiscussion[]
  ticketLinkLinkeds?: TicketLink[]
  ticketLinkTickets?: TicketLink[]
  ticketReviews?: TicketReview[]
}

export const MainTypes = ['Task', 'Support', 'Change Request', 'Shopping List', 'Other'] as const
export type MainType = (typeof MainTypes)[number]

export const Statuses = [
  'New',
  'Active',
  'In Development',
  'Waiting on Customer',
  'Resolved',
  'Invoiced',
  'Cancelled',
  'Deleted',
] as const
export type Status = (typeof Statuses)[number]

export const Sources = ['Email', 'Portal', 'Other'] as const
export type Source = (typeof Sources)[number]

export const Categories = ['Change Request', 'Inquiry'] as const
export type Category = (typeof Categories)[number]

export const ticketInfo: Info<Ticket> = {
  typeName: 'Ticket',
  nameKey: 'name',
  sortKey: 'code',
  backend: 'Ticket',
  endpoint: 'Ticket',
  formSize: 'medium',
  lowlight: (x) => !x.billable,
  fields: {
    code: { label: 'Code', type: 'smalltext', disabled: true },
    projectId: { label: 'Project', type: 'external', required: true },
    name: { label: 'Title', type: 'bigtext', required: true },
    description: { label: 'Description', type: 'textarea' },
    mainType: { label: 'Type', type: 'codetype', required: true },
    subType: { label: 'Sub Type', type: 'smalltext' },
    category: { label: 'Category', type: 'select' },
    status: { label: 'Status', type: 'codetype', required: true },
    priority: { label: 'Priority', type: 'codetype', required: true },
    source: { label: 'Source', type: 'codetype', required: true },
    sourceId: { label: 'Source ID', type: 'smalltext' },
    raisedOn: { label: 'Raised On', type: 'date', required: true },
    raisedBy: { label: 'Raised By', type: 'smalltext' },
    endDate: { label: 'End Date', type: 'date' },
    assignee: { label: 'Assigned to', type: 'external' },
    customerId: { label: 'Customer', type: 'bigtext' },
    contractId: { label: 'Contract', type: 'external' },
    reviewFlag: { label: 'Review Flag', type: 'smalltext' },
    module: { label: 'Module', type: 'smalltext' },
    isPublic: { label: 'Public', type: 'bool', required: true },
    billMethod: { label: 'Billing Method', type: 'bigtext' },
    billable: { label: 'Billable', type: 'bool', required: true },
    workMinutes: { label: 'Work Time', type: 'time', disabled: true },
    ticketBillings: { label: 'Billings', type: 'objlist' },
    ticketDevOpsLinks: { label: 'DevOps Links', type: 'objlist' },
    ticketDiscussions: { label: 'Discussions', type: 'objlist' },
    ticketLinkLinkeds: { label: 'Linked Tickets', type: 'objlist' },
    ticketLinkTickets: { label: 'Link Tickets', type: 'objlist' },
    ticketReviews: { label: 'Reviews', type: 'objlist' },
    ...TenantEntityInfo.fields,
  },
  options: {
    projectId: { entity: 'Project' },
    assignee: { entity: 'Employee' },
    mainType: { typeCode: 'TicketType' },
    status: { typeCode: 'TicketStatus' },
    priority: { typeCode: 'Priority' },
    source: { typeCode: 'TicketSource' },
    category: { options: Categories.slice() },
    ticketBillings: { entity: 'TicketBilling' },
    ticketDevOpsLinks: { entity: 'TicketDevOpsLink' },
    ticketDiscussions: { entity: 'TicketDiscussion' },
    ticketLinkLinkeds: { entity: 'TicketLink' },
    ticketLinkTickets: { entity: 'TicketLink' },
    ticketReviews: { entity: 'TicketReview' },
    contractId: { entity: 'ProjectContract' },
    ...TenantEntityInfo.options,
  },
  default: {
    projectId: '',
    priority: '2',
    code: 'Auto-generated',
    name: '',
    mainType: '01',
    status: '01',
    source: '01',
    billable: true,
    workMinutes: 0,
    raisedOn: new Date(),
    isPublic: false,
  },
  columnsShown: new Set([
    'projectId',
    'code',
    'name',
    'mainType',
    'priority',
    'status',
    'raisedOn',
    'raisedBy',
    'assignee',
    'workMinutes',
  ]),
  formLayout: [
    ['projectId', 'code', 'name'],
    ['mainType', 'status', 'priority'],
    ['raisedOn', 'raisedBy', 'billable'],
    ['source', 'sourceId', 'assignee'],
    ['description'],
    ['isPublic', 'workMinutes'],
  ],
  tabList: {
    TicketDiscussion: { tabName: 'Discussion', relation: { id: 'ticketId' } },
    TicketReview: { tabName: 'Review', relation: { id: 'ticketId' } },
    TicketLink: { tabName: 'Linked Tickets', relation: { id: 'ticketId' } },
    TicketDevOpsLink: { tabName: 'DevOps Tickets', relation: { id: 'ticketId' } },
    TicketBilling: { tabName: '$$$', relation: { id: 'ticketId' } },
    ProjectTimeLog: { tabName: 'Time Log', relation: { projectId: 'projectId', id: 'ticketId' } },
    TicketEmail: { tabName: 'Email', relation: {} },
  },
}
