<script setup lang="ts">
import { BaseEntity } from '../../data/entities/BaseEntity'
import { BuildApi, UseApi } from '../../services/api/useApi'
import { useModal } from 'vuestic-ui'
import { bindContext, EntityPage } from '../../services/entityPage'
import { schema, SchemaKey } from '../../data/schema'
import { useI18n } from 'vue-i18n'
import { getTypeName, saveOrAdd, editOrAdd } from '../../services/utils'

import EntityTable from './EntityTable.vue'
import EntityForm from './EntityForm.vue'
import EntityFilters from './EntityFilters.vue'
import EntityModify from './EntityModify.vue'

const props = defineProps<{
  type: SchemaKey
  api?: BuildApi<BaseEntity>
}>()

const { t } = useI18n()

const api = props.api ?? (await new UseApi<BaseEntity>(props.type).build())
const page = new EntityPage<BaseEntity>(api as any, useModal().confirm, props.type)
bindContext(page as any)
</script>

<template>
  <h1 class="page-title">{{ t('menu.' + ($route.name as string)) }}</h1>

  <VaCard>
    <VaCardContent>
      <slot name="top" />
      <EntityFilters :type="type" :api="api" :page="page">
        <template #left>
          <slot name="filter-left" />
        </template>
        <template #right>
          <slot name="filter-right" />
        </template>
      </EntityFilters>
      <slot name="Q2" />
      <EntityTable
        :ref="page.tableRef"
        v-model:sort-by="api.sorting.value.sortBy"
        v-model:sorting-order="api.sorting.value.sortingOrder"
        v-model:pagination="api.pagination.value"
        :type="type"
        :loading="api.isLoading.value"
        :entities="api.objs.value"
        @edit="page.editEntity"
        @delete="page.deleteEntity"
        @save="page.onEntitySaved"
      />
      <slot name="Q3" />
      <EntityModify :type="type" :api="api" :page="page">
        <template #left>
          <slot name="modify-left" />
        </template>
        <template #right>
          <slot name="modify-right" />
        </template>
      </EntityModify>
      <slot name="bottom" />
    </VaCardContent>
  </VaCard>

  <VaModal
    v-slot="{ cancel, ok }"
    v-model="page.showEditForm.value"
    :size="schema[type].formSize || 'small'"
    mobile-fullscreen
    close-button
    hide-default-actions
    :before-cancel="page.beforeEditFormCloses"
  >
    <h1 class="va-h5">
      {{ editOrAdd(page) + ' ' + getTypeName(type) }}
    </h1>
    <EntityForm
      :ref="page.editFormRef"
      :type="type"
      :entity="page.entityToEdit.value"
      :save-button-label="saveOrAdd(page)"
      @close="cancel"
      @save="
        (entity: BaseEntity) => {
          page.onEntitySaved(entity)
          ok()
        }
      "
      @delete="
        (id: string) => {
          page.onEntityDelete(id)
          ok()
        }
      "
    >
      <template #top>
        <slot name="form-top" />
      </template>
      <template #bottom>
        <slot name="form-bottom" />
      </template>
      <template #button-row>
        <slot name="form-button-row" />
      </template>
    </EntityForm>
  </VaModal>
</template>
