<script setup lang="ts">
import { computed, nextTick, ref, watch, onMounted, watchEffect } from 'vue'
import { useModal, VaModal } from 'vuestic-ui'
import { BuildApi, UseApi } from '../../services/api/useApi'
import { EntityPage, bindContext } from '../../services/entityPage'
import { schema, SchemaKey } from '../../data/schema'
import { BaseEntity } from '../../data/entities/BaseEntity'
import { getTypeName, saveOrAdd, editOrAdd } from '../../services/utils'
import { useI18n } from 'vue-i18n'
import TicketEmailTab from './TicketEmailTab.vue'  

import EntityTable from '../../components/entity-dataview/EntityTable.vue'
import EntityForm from '../../components/entity-dataview/EntityForm.vue'
import EntityFilters from './EntityFilters.vue'
import EntityModify from './EntityModify.vue'
import EntityTabOverview from './EntityTabOverview.vue'
import { PRecord } from '../../data/FieldTypes'

import { useRouter } from 'vue-router'

const router = useRouter()
const route = useRouter().currentRoute

const tabIndex = ref(0)
const { confirm } = useModal()
const { t } = useI18n()

const props = defineProps<{
  type: SchemaKey
}>()

//type TabLogic = PRecord<SchemaKey, { api: BuildApi<BaseEntity>; page: EntityPage<BaseEntity> }>
type TabLogic = Record<string, { api: BuildApi<BaseEntity>; page: EntityPage<BaseEntity> } | null>


// Step 1: Build API and Entity Page
const entityApi = await new UseApi<BaseEntity>(props.type).build()
const entityPage = new EntityPage<BaseEntity>(entityApi as any, confirm, props.type)
bindContext(entityPage as any)

//  Step 2: React to selectedId
watch(
  () => route.value.query.selectedId,
  async (selectedId) => {
    if (!selectedId || typeof selectedId !== 'string') return

    try {
      const entity = await entityApi.get(selectedId)
      entityPage.entityToEdit.value = entity
      await entityPage.preEditHook(entity)
      entityPage.showEditForm.value = true
    } catch (e) {
      console.error(' Failed to load entity from selectedId:', selectedId, e)
    }
  },
  { immediate: true },
)

watch(
  () => entityPage.showEditForm.value,
  (isOpen) => {
    if (!isOpen && route.value.query.selectedId) {
      const newQuery = { ...route.value.query }
      delete newQuery.selectedId
      router.replace({ query: newQuery })
    }
  },
)

onMounted(async () => {
  const selectedId = route.value.query.selectedId
  if (!selectedId || typeof selectedId !== 'string') return

  try {
    const entity = await entityApi.get(selectedId)
    entityPage.entityToEdit.value = entity
    await entityPage.preEditHook(entity)

    // Let DOM fully mount before toggling modal
    await nextTick()
    setTimeout(() => {
      entityPage.showEditForm.value = true
    }, 0)
  } catch (e) {
    console.error(' Failed to open modal on mount:', e)
  }
})

watchEffect(async () => {
  const selectedId = route.value.query.selectedId

  if (!selectedId || typeof selectedId !== 'string') return

  try {
    const entity = await entityApi.get(selectedId)
    entityPage.entityToEdit.value = entity
    await entityPage.preEditHook(entity)

    //  Ensure DOM is fully updated before toggling modal visibility
    await nextTick()
    entityPage.showEditForm.value = false // <-- force re-render trick
    await nextTick()
    entityPage.showEditForm.value = true // <-- now show for sure
  } catch (e) {
    console.error(' Failed to load entity from selectedId:', selectedId, e)
  }
})

//  Step 3: Set up tabs
const tabList = schema[props.type].tabList!
const render = ref(true)

const tabs = Object.fromEntries(
  await Promise.all(
    Object.keys(tabList).map(async (tab) => {
      const _tab = tab as SchemaKey

      // No page
      if (tabList[_tab]?.relation && Object.keys(tabList[_tab].relation).length === 0) {
        return [_tab, null] 
      }

      const api = await new UseApi<BaseEntity>(_tab).build({ noFetch: true })
      const page = new EntityPage<BaseEntity>(api, confirm, _tab)
      bindContext(page)
      return [_tab, { api, page }]
    }),
  ),
) as TabLogic

// step 4: filter the tab entities based on the entity being edited
entityPage.preEditHook = async (entity: BaseEntity) => {
  for (const tab in tabs) {
    const _tab = tab as SchemaKey
    const filter = Object.fromEntries(
      Object.entries(makeDefaults(entity, _tab)!).map(([k, v]) => [k, new Set([v])]),
    ) as Record<string, Set<string>>
    tabs[_tab]!.api.filters.value.fieldValues = filter
  }
}

//  Step 5: Helpers
//const tabKey = computed(() => Object.keys(tabs)[tabIndex.value] as SchemaKey)
const tabKey = computed(() => Object.keys(tabs)[tabIndex.value] as string)
const tabPage = computed(() => tabs[tabKey.value]!.page)
const tabApi = computed(() => tabs[tabKey.value]!.api)
const tabDefault = computed(() => makeDefaults(entityPage.entityToEdit?.value, tabKey.value))

// set main entity as default for tab entity fields
const makeDefaults = (entity: any, tabName: SchemaKey) => {
  if (!entity) {
    return null
  }
  return Object.fromEntries(
    Object.entries(tabList[tabName]!.relation).map(([key, value]) => [value, entity[key]]),
  ) as Record<string, any>
}

// render the tab table when the tab changes
watch(tabIndex, async () => {
  render.value = false
  await nextTick()
  render.value = true
})
</script>

<template>
  <h1 class="page-title">{{ t('menu.' + ($route.name as string)) }}</h1>
  <VaCard>
    <VaCardContent>
      <slot name="top" />
      <EntityFilters :type="type" :api="entityApi" :page="entityPage">
        <template #left>
          <slot name="filter-left" />
        </template>
        <template #right>
          <slot name="filter-right" />
        </template>
      </EntityFilters>
      <EntityTable
        :ref="entityPage.tableRef"
        v-model:sort-by="entityApi.sorting.value.sortBy"
        v-model:sorting-order="entityApi.sorting.value.sortingOrder"
        v-model:pagination="entityApi.pagination.value"
        :loading="entityApi.isLoading.value"
        :entities="entityApi.objs.value"
        :type="type"
        @edit="entityPage.editEntity"
        @delete="entityPage.deleteEntity"
        @save="entityPage.onEntitySaved"
      />
      <EntityModify :type="type" :api="entityApi" :page="entityPage">
        <template #left>
          <slot name="modify-left" />
        </template>
        <template #right>
          <slot name="modify-right" />
        </template>
      </EntityModify>
      <slot name="bottom" />
    </VaCardContent>

    <VaModal
      v-slot="{ cancel, ok }"
      v-model="entityPage.showEditForm.value"
      :fullscreen="!!entityPage.entityToEdit.value"
      :size="schema[type].formSize || 'medium'"
      close-button
      stateful
      hide-default-actions
      :before-cancel="entityPage.beforeEditFormCloses"
    >
      <h1 class="va-h5">
        {{ editOrAdd(entityPage) + ' ' + getTypeName(type) }}
      </h1>
      <EntityForm
        :ref="entityPage.editFormRef"
        :type="type"
        :entity="entityPage.entityToEdit.value"
        :save-button-label="saveOrAdd(entityPage)"
        @close="cancel"
        @save="
          (tabEntity) => {
            entityPage.onEntitySaved(tabEntity)
            ok()
          }
        "
        @delete="
          (id: string) => {
            entityPage.onEntityDelete(id)
            ok()
          }
        "
      >
        <template #top>
          <slot name="form-top" />
        </template>
        <template #bottom>
          <slot name="form-bottom" />
        </template>
        <template #button-row>
          <slot name="form-button-row" />
        </template>
      </EntityForm>
      <!-- Create a unique Project Overview tab-->
      <template v-if="entityPage.entityToEdit.value !== null">
        <VaTabs v-model="tabIndex" class="w-full mb-2 mt-4">
          <template #tabs>
            <VaTab v-for="tab in Object.keys(tabs)" :key="tab">
              {{ tabList[tab as SchemaKey]!.tabName }}
            </VaTab>
          </template>
        </VaTabs>
      <!-- Populate the tab with unique data -->
        <template v-if="render">
          <template v-if="tabKey === 'ProjectOverview'">
              <EntityTabOverview
              :entity="entityPage.entityToEdit.value"
              :contracts="(() => {
                const contractKey = Object.keys(tabList).find(
                  (key) => tabList[key as SchemaKey]?.tabName === 'Contracts'
                ) as SchemaKey | undefined
                return contractKey && tabs[contractKey]?.api.objs.value ? tabs[contractKey].api.objs.value : []
              })()"
              :related-parties="(() => {
                const relatedPartiesKey = Object.keys(tabList).find(
                  (key) => tabList[key as SchemaKey]?.tabName === 'Related Parties'
                ) as SchemaKey | undefined
                return relatedPartiesKey && tabs[relatedPartiesKey]?.api.objs.value ? tabs[relatedPartiesKey].api.objs.value : []
              })()"
              :tasks="(() => {
                const tasksKey = Object.keys(tabList).find(
                  (key) => tabList[key as SchemaKey]?.tabName === 'Tasks'
                ) as SchemaKey | undefined
                return tasksKey && tabs[tasksKey]?.api.objs.value ? tabs[tasksKey].api.objs.value : []
              })()"
              :tickets="(() => {
                const ticketsKey = Object.keys(tabList).find(
                  (key) => tabList[key as SchemaKey]?.tabName === 'Tickets'
                ) as SchemaKey | undefined
                return ticketsKey && tabs[ticketsKey]?.api.objs.value ? tabs[ticketsKey].api.objs.value : []
              })()"
              :payments="(() => {
                const paymentsKey = Object.keys(tabList).find(
                  (key) => tabList[key as SchemaKey]?.tabName === 'Payment Facts'
                ) as SchemaKey | undefined
                return paymentsKey && tabs[paymentsKey]?.api.objs.value ? tabs[paymentsKey].api.objs.value : []
              })()"
              :time-logs="(() => {
                const timeLogsKey = Object.keys(tabList).find(
                  (key) => tabList[key as SchemaKey]?.tabName === 'Time Logs'
                ) as SchemaKey | undefined
                return timeLogsKey && tabs[timeLogsKey]?.api.objs.value ? tabs[timeLogsKey].api.objs.value : []
              })()"
              />
          </template>
          <!-- Populate other tabs -->
          <template v-else>
            <EntityFilters :type="tabKey" :api="tabApi" :page="tabPage" />
            <EntityTable
              :ref="tabPage.tableRef"
              v-model:sort-by="tabApi.sorting.value.sortBy"
              v-model:sorting-order="tabApi.sorting.value.sortingOrder"
              v-model:pagination="tabApi.pagination.value"
              :loading="tabApi.isLoading.value"
              :entities="tabApi.objs.value"
              :type="tabKey"
              @edit="tabPage.editEntity"
              @delete="tabPage.deleteEntity"
              @save="tabPage.onEntitySaved"
            />
            <EntityModify :type="tabKey" :api="tabApi" :page="tabPage" />
          </template>
        </template>
      </template>
    </VaModal>
    <VaModal
      v-slot="{ cancel, ok }"
      v-model="tabPage.showEditForm.value"
      :size="schema[tabKey].formSize || 'small'"
      mobile-fullscreen
      close-button
      hide-default-actions
      :before-cancel="tabPage.beforeEditFormCloses"
    >
      <h1 class="va-h5">{{ editOrAdd(tabPage) + ' ' + getTypeName(tabKey) }}</h1>
      <EntityForm
        :ref="tabPage.editFormRef"
        :type="tabKey"
        :entity="tabPage.entityToEdit.value"
        :defaults="tabDefault"
        :save-button-label="saveOrAdd(tabPage)"
        @close="cancel"
        @save="
          (tabEntity) => {
            tabPage.onEntitySaved(tabEntity)
            ok()
          }
        "
        @delete="
          (id: string) => {
            tabPage.onEntityDelete(id)
            ok()
          }
        "
      />
    </VaModal>
  </VaCard>
</template>

<!-- <style lang="scss">
.va-modal__close--fullscreen {
  display: none;
}
</style> -->
