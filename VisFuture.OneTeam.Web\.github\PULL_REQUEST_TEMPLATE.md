<!--
<PERSON><PERSON> SURE TO READ THE CONTRIBUTING <PERSON><PERSON>DE BEFORE CREATING A PR
https://github.com/epicmaxco/vuestic-admin/blob/master/CODE_OF_CONDUCT.md
-->

<!-- Provide a general summary of your changes in the Title above -->
<!-- Keep the title short and descriptive, as it will be used as a commit message -->

## Description

<!-- Describe your changes in detail -->

## Markup:

<!-- Paste your markup here. -->
<details>

```vue
// Your code
```

</details>

## Types of changes

<!-- What types of changes does your code introduce? Put an `x` in all the boxes that apply: -->

- [ ] Bug fix (non-breaking change which fixes an issue)
- [ ] New feature (non-breaking change which adds functionality)
- [ ] Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] Improvement/refactoring (non-breaking change that doesn't add any feature but make things better)
