<script setup lang="ts">
import { computed, Ref } from 'vue'
import { Pagination } from '../../services/api/api'

const pagination = defineModel({ required: true }) as Ref<Pagination>
const totalPages = computed(() => Math.ceil(pagination.value.total / pagination.value.perPage))
</script>

<template>
  <div class="flex flex-col-reverse md:flex-row gap-2 justify-between items-center py-2">
    <div>
      <b>{{ pagination.total }} results.</b>
      Results per page
      <VaSelect v-model="pagination.perPage" class="!w-20" :options="[10, 50, 100]" />
    </div>

    <div v-if="totalPages > 1" class="flex">
      <VaButton
        preset="secondary"
        icon="va-arrow-left"
        aria-label="Previous page"
        :disabled="pagination.page === 1"
        @click="pagination.page--"
      />
      <VaPagination
        v-model="pagination.page"
        buttons-preset="secondary"
        :pages="totalPages"
        :visible-pages="5"
        :boundary-links="false"
        :direction-links="false"
      />
      <VaButton
        class="mr-2"
        preset="secondary"
        icon="va-arrow-right"
        aria-label="Next page"
        :disabled="pagination.page === totalPages"
        @click="pagination.page++"
      />
    </div>
  </div>
</template>
