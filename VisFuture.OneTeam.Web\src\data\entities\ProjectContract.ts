import { round } from '../../services/utils'
import { Info } from '../FieldTypes'
import { TenantEntity, TenantEntityInfo } from './TenantEntity'

export interface ProjectContract extends TenantEntity {
  projectId: string
  name: string
  description?: string
  relatedPartyA: string // Guid !!!!
  relatedPartyB: string
  relatedPartyC?: string
  relatedPartyD?: string
  mainType: string
  subType?: ContractSubType
  currency: string
  amount: number
  taxRate: number
  taxAmount: number
  totalAmount: number
  effectiveDate: Date
  sumMethod: string
}

export const ContractSubTypes = ['Fixed', 'TimeAndMaterial'] as const
export type ContractSubType = (typeof ContractSubTypes)[number]

export const projectContractInfo: Info<ProjectContract> = {
  typeName: 'Project Contract',
  nameKey: 'name',
  sortKey: 'name',
  backend: 'Project',
  endpoint: 'Contract',
  fields: {
    projectId: { label: 'Project', type: 'external', required: true },
    name: { label: 'Contract Name', type: 'bigtext', required: true },
    description: { label: 'Description', type: 'textarea' },
    relatedPartyA: { label: 'Party A', type: 'external', required: true },
    relatedPartyB: { label: 'Party B', type: 'external', required: true },
    relatedPartyC: { label: 'Party C', type: 'external' },
    relatedPartyD: { label: 'Party D', type: 'external' },
    mainType: { label: 'Main Type', type: 'codetype', required: true },
    subType: { label: 'Sub Type', type: 'select' },
    currency: { label: 'Currency', type: 'codetype', required: true },
    amount: { label: 'Amount', type: 'currency', required: true },
    taxRate: { label: 'Tax Rate', type: 'percent', required: true },
    taxAmount: { label: 'Tax Amount', type: 'currency', required: true },
    totalAmount: { label: 'Total Amount', type: 'currency', required: true },
    effectiveDate: { label: 'Effective Date', type: 'date', required: true },
    sumMethod: { label: 'Sum Method', type: 'codetype', required: true },
    ...TenantEntityInfo.fields,
  },
  options: {
    amount: {
      currencyKey: 'currency',
      sync: (x) => {
        x.value.taxAmount = round(x.value.amount * x.value.taxRate * 0.01)
        x.value.totalAmount = round(x.value.amount + x.value.taxAmount)
      },
    },
    taxRate: { sync: (x) => (x.value.taxAmount = round(x.value.amount * x.value.taxRate * 0.01)) },
    taxAmount: {
      currencyKey: 'currency',
      sync: (x) => (x.value.totalAmount = round(x.value.amount + x.value.taxAmount)),
    },
    totalAmount: { currencyKey: 'currency' },
    relatedPartyA: { entity: 'ProjectRelatedParty', joins: { projectId: 'projectId' } },
    relatedPartyB: { entity: 'ProjectRelatedParty', joins: { projectId: 'projectId' } },
    relatedPartyC: { entity: 'ProjectRelatedParty', joins: { projectId: 'projectId' } },
    relatedPartyD: { entity: 'ProjectRelatedParty', joins: { projectId: 'projectId' } },
    currency: { typeCode: 'Currency' },
    projectId: { entity: 'Project' },
    mainType: { typeCode: 'ContractType' },
    subType: { options: ContractSubTypes.slice() },
    sumMethod: { typeCode: 'ContractSumMethod' },
    ...TenantEntityInfo.options,
  },
  default: {
    projectId: '',
    name: '',
    relatedPartyA: '',
    relatedPartyB: '',
    mainType: '01',
    currency: 'CAD',
    amount: 0,
    taxRate: 0,
    taxAmount: 0,
    totalAmount: 0,
    effectiveDate: new Date(),
    sumMethod: '1',
  },
  columnsShown: new Set(['name', 'mainType', 'amount', 'totalAmount', 'effectiveDate', 'sumMethod']),
  formLayout: [
    ['name', 'projectId'],
    ['relatedPartyA', 'relatedPartyB'],
    ['relatedPartyC', 'relatedPartyD'],
    ['mainType', 'currency'],
    ['amount', 'sumMethod'],
    ['taxRate', 'taxAmount'],
    ['totalAmount', 'effectiveDate'],
    ['description'],
  ],
}
