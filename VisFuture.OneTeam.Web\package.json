{"name": "oneteam-pro", "private": true, "version": "0.0.1", "type": "module", "scripts": {"prepare": "husky install", "dev": "vite", "build": "npm run lint && vue-tsc --noEmit && vite build", "build:ci": "vite build", "start:ci": "serve -s ./dist", "prelint": "npm run format", "lint": "eslint \"./src/**/*.{ts,js,vue}\" --fix", "format": "prettier --write .", "preview": "vite preview", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "lint-staged": {"./src/**/*.{ts,js,vue}": ["npm run lint"]}, "dependencies": {"@gtm-support/vue-gtm": "^2.0.0", "@tanstack/vue-query": "^5.62.2", "@vuestic/tailwind": "^0.1.3", "@vueuse/core": "^10.6.1", "chart.js": "^4.4.1", "chartjs-chart-geo": "^4.2.8", "epic-spinners": "^2.0.0", "flag-icons": "^6.15.0", "ionicons": "^4.6.3", "medium-editor": "^5.23.3", "pinia": "^2.1.7", "register-service-worker": "^1.7.1", "sass": "^1.69.5", "serve": "^14.2.1", "vue": "3.5.8", "vue-chartjs": "^5.3.0", "vue-i18n": "^9.6.2", "vue-router": "^4.2.5", "vue-to-print": "^1.4.0", "vuestic-ui": "^1.10.2"}, "devDependencies": {"@chromatic-com/storybook": "3", "@intlify/unplugin-vue-i18n": "^1.5.0", "@storybook/addon-essentials": "^8.6.11", "@storybook/addon-interactions": "^8.6.11", "@storybook/addon-links": "^8.6.11", "@storybook/blocks": "^8.6.11", "@storybook/test": "^8.6.11", "@storybook/vue3": "^8.6.11", "@storybook/vue3-vite": "^8.6.11", "@types/file-saver": "^2.0.7", "@types/medium-editor": "^5.0.5", "@types/node": "^20.9.0", "@typescript-eslint/eslint-plugin": "^6.11.0", "@typescript-eslint/parser": "^6.11.0", "@vitejs/plugin-vue": "^5.2.1", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "autoprefixer": "^10.4.13", "eslint": "^8.13.0", "eslint-plugin-prettier": "^5.0.1", "eslint-plugin-storybook": "^0.12.0", "eslint-plugin-vue": "^9.18.1", "husky": "^8.0.1", "lint-staged": "^15.1.0", "postcss": "^8.4.21", "prettier": "^3.1.0", "storybook": "^8.6.11", "tailwindcss": "^3.4.0", "typescript": "^5.2.2", "vite": "^5.4.9", "vue-eslint-parser": "^9.3.2", "vue-tsc": "^2.1.6"}}