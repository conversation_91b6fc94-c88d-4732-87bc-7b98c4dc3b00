import { Info } from '../FieldTypes'
import { OrganizationHierarchy } from './OrganizationHierarchy'
import { organizationHierarchyInfo } from './OrganizationHierarchy'

export const organizationByEmployeeInfo: Info<OrganizationHierarchy> = {
  ...organizationHierarchyInfo,
  endpoint: 'OrganizationEmployeeList', // reuse existing endpoint if it filters by employeeId
  typeName: 'Organization',
  nameKey: 'name',
  typeShortName: 'Organization',
}
