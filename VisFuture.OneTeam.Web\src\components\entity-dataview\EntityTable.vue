<script setup lang="ts">
import { defineVaDataTableColumns } from 'vuestic-ui'
import { computed, ref, Ref, watch } from 'vue'
import { BaseEntity } from '../../data/entities/BaseEntity'
import { Pagination } from '../../services/api/api'
import { BaseKey, Info } from '../../data/FieldTypes'
import { schema, SchemaKey } from '../../data/schema'
import { getTypeName } from '../../services/utils'
import { getExternMap, isExtern } from '../../services/externUtils'

import PaginationNav from './PaginationNav.vue'
import EntityFormInput from './EntityFormInput.vue'
import EntityTableCell from './EntityTableCell.vue'

const props = defineProps<{
  entities: BaseEntity[]
  type: SchemaKey
  loading: boolean
}>()

const emit = defineEmits<{
  (event: 'edit', entity: any): Promise<void>
  (event: 'delete', entity: any): Promise<void>
  (event: 'save', entity: any, inline?: boolean): Promise<void>
}>()

const info = schema[props.type] as Info<BaseEntity>

// column handling
type Column = {
  label: string
  key: BaseKey
  sortable: boolean
}
const makeColumns = () => {
  // create columns from fields
  const arr: Column[] = []
  Object.keys(info.fields).forEach((x) => {
    const _x = x as BaseKey
    if (!info.columnsShown.has(_x)) {
      // skip columns not shown
      return
    }
    arr.push({
      label: info.fields[_x].label,
      key: _x,
      sortable: true,
    })
  })
  return defineVaDataTableColumns([...arr, { label: ' ', key: 'actions', sortable: false }])
}
const columns = ref(makeColumns())
const toggleColumn = async (entry: ContextEntry) => {
  // toggle column visibility (context menu select)
  info.columnsShown.has(entry.value) ? info.columnsShown.delete(entry.value) : info.columnsShown.add(entry.value)
  columns.value = makeColumns()
  contextMenu.value = makeMenu()
  allLoading.value = true
  await makeExternList()
}

// external handling
const allLoading = ref(true) // tracks if any of the cells are still loading
const externList = ref<Record<string, Record<string, BaseEntity>>>({})
const makeExternList = async () => {
  const tempList: typeof externList.value = {}
  // get mapping from ids to entities for each external
  for (const key of Object.keys(info.fields)) {
    const _key = key as BaseKey
    if (!info.columnsShown.has(_key) || !isExtern(props.type, _key)) {
      continue
    }
    const idSet = new Set(props.entities.map((x) => x[_key])) as Set<string>
    idSet.delete(null as any)
    idSet.delete(undefined as any)
    idSet.delete('' as any)
    tempList[key] = await getExternMap([...idSet], props.type, _key)
  }

  externList.value = tempList // this is so the table can be written in a single paint
  allLoading.value = false
}
watch(
  () => props.loading,
  async () => {
    if (!props.loading) {
      allLoading.value = true
      await makeExternList()
    }
  },
  { deep: true, immediate: true },
)

// handle row highlighting
const bindRow = (row: BaseEntity) => {
  const classes = [] as string[]
  if (info.highlight && info.highlight(row, externList.value)) {
    classes.push('row-highlight')
  }
  if (info.lowlight && info.lowlight(row, externList.value)) {
    classes.push('row-lowlight')
  }
  return {
    class: classes.join(' '),
  }
}

// make context menu
type ContextEntry = {
  text: string
  value: BaseKey
  icon: string
}
const makeMenu = () =>
  Object.keys(info.fields).map((x) => {
    const _x = x as BaseKey
    return {
      text: info.fields[_x].label,
      value: x,
      icon: info.columnsShown.has(_x) ? 'check_box' : 'check_box_outline_blank',
    } as ContextEntry
  })

const contextMenu: Ref<ContextEntry[]> = ref(makeMenu())
const sortBy = defineModel('sortBy', { required: true }) as Ref<BaseKey>
const sortingOrder = defineModel('sortingOrder', { required: true }) as Ref<'asc' | 'desc' | null>
const pagination = defineModel('pagination', { required: true }) as Ref<Pagination>
const selectedRows = ref<BaseEntity[]>([])
defineExpose({ selectedRows })
</script>

<template>
  <VaMenu :options="contextMenu" preset="context" class="w-full h-fit" @selected="(v: ContextEntry) => toggleColumn(v)">
    <template #anchor>
      <VaDataTable
        v-model="selectedRows"
        v-model:sort-by="sortBy"
        v-model:sorting-order="sortingOrder"
        :columns="columns"
        :items="props.entities"
        :loading="allLoading"
        :row-bind="bindRow"
        animated
        selectable
        select-mode="multiple"
      >
        <template v-for="(field, key) in info.fields" #[`cell(${key})`]="{ rowData }" :key="key">
          <VaValue v-slot="showInput">
            <EntityFormInput
              v-if="showInput.value"
              v-model="
                computed({
                  get: () => rowData,
                  set: () => {},
                }).value
              "
              :obj-key="key"
              :type="props.type"
              :inline="true"
              @focusout="
                () => {
                  emit('save', rowData, true)
                  showInput.value = false
                }
              "
            />
            <span
              v-else
              class="table-inline__item w-full h-full"
              :class="showInput.value ? 'table-inline__item--hidden' : ''"
              @dblclick="showInput.value = true"
            >
              <EntityTableCell
                :obj-key="key"
                :type="props.type"
                :row-data="rowData"
                :field="field"
                :extern-list="externList"
              />
            </span>
          </VaValue>
        </template>

        <template #cell(actions)="{ rowData }">
          <span class="flex gap-2 justify-end">
            <VaButton
              preset="primary"
              size="small"
              icon="mso-edit"
              :aria-label="'Edit ' + getTypeName(type)"
              @click="emit('edit', rowData as BaseEntity)"
            />
            <VaButton
              preset="primary"
              size="small"
              icon="mso-delete"
              color="danger"
              :aria-label="'Delete ' + getTypeName(type)"
              @click="emit('delete', rowData as BaseEntity)"
            />
          </span>
        </template>
      </VaDataTable>
    </template>
  </VaMenu>
  <PaginationNav v-model="pagination" />
</template>

<style lang="scss">
.va-data-table {
  ::v-deep(.va-data-table__table-tr) {
    border-bottom: 1px solid var(--va-background-border);
  }
}
.row-highlight {
  background-color: var(--va-warning) !important;
}
.row-lowlight {
  background-color: var(--va-background-element);
  font-style: italic;
}
</style>
