export interface B64File {
  mimeType: string
  fileName: string
  data: string
}

/** download file to client machine
 * @param {B64File} file - The file to download.
 */
export const downloadFile = async (file: B64File) => {
  const downloadLink = document.createElement('a')
  downloadLink.href = `data:${file.mimeType};base64,${file.data}`
  downloadLink.download = file.fileName
  downloadLink.click()
}

/** convert file to base64
 * @param {File} file - The file to convert.
 * @returns {Promise<B64File>} The converted file.
 */
export const fileToB64 = async (file: File): Promise<B64File> => {
  const data: string = await new Promise((resolve) => {
    const reader = new FileReader()
    reader.onloadend = () => {
      const result = reader.result as string | null
      if (result === null) {
        throw new Error('FileReader result is null')
      }
      resolve(result.split(',')[1])
    }
    reader.readAsDataURL(file)
  })
  return {
    mimeType: file.type,
    fileName: file.name,
    data,
  }
}
