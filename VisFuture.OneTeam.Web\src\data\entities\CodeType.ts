import { Info } from '../FieldTypes'
import { TenantEntity, TenantEntityInfo } from './TenantEntity'
import { CodeItem } from './CodeItem'

export interface CodeType extends TenantEntity {
  typeCode: string
  name: string
  description?: string
  mainType: CodeMainType
  subType?: CodeSubType
  superiorId?: string
  codeItems?: CodeItem[]
}

export const CodeMainTypes = ['System', 'User'] as const
export type CodeMainType = (typeof CodeMainTypes)[number]

export const CodeSubTypes = ['General', 'Specific', 'Custom'] as const
export type CodeSubType = (typeof CodeSubTypes)[number]

export const codeTypeInfo: Info<CodeType> = {
  typeName: 'Code Type',
  nameKey: 'name',
  sortKey: 'typeCode',
  sortAsc: true,
  backend: 'BaseBiz',
  endpoint: 'CodeType',
  fields: {
    name: { label: 'Name', type: 'bigtext', required: true },
    typeCode: { label: 'Type Code', type: 'smalltext', required: true },
    description: { label: 'Description', type: 'textarea' },
    mainType: { label: 'Main Type', type: 'codetype', required: true },
    subType: { label: 'Sub Type', type: 'smalltext' },
    superiorId: { label: 'Superior Type', type: 'external' },
    codeItems: { label: 'Code Items', type: 'external' },
    ...TenantEntityInfo.fields,
  },
  options: {
    mainType: { typeCode: 'ConfigType' },
    superiorId: { entity: 'CodeType' },
    codeItems: { entity: 'CodeItem' },
    ...TenantEntityInfo.options,
  },
  default: {
    typeCode: '',
    name: '',
    mainType: 'User',
  },
  columnsShown: new Set(['name', 'typeCode', 'mainType', 'superiorId']),
  formLayout: [['name', 'typeCode'], ['mainType', 'superiorId'], ['description']],
  tabList: {
    CodeItem: { tabName: 'Code Items', relation: { id: 'codeTypeId' } },
  },
}
